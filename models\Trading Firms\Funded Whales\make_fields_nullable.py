import os
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor and set autocommit to True
    conn.autocommit = True
    cur = conn.cursor()
    
    # Alter the session_id column to allow NULL values
    print("Altering session_id column to allow NULL values...")
    cur.execute("ALTER TABLE completeordermodel ALTER COLUMN session_id DROP NOT NULL;")
    print("session_id column altered successfully")
    
    # Alter the terminal_id column to allow NULL values
    print("Altering terminal_id column to allow NULL values...")
    cur.execute("ALTER TABLE completeordermodel ALTER COLUMN terminal_id DROP NOT NULL;")
    print("terminal_id column altered successfully")
    
    # Verify the changes
    print("\nVerifying changes...")
    cur.execute("""
        SELECT column_name, is_nullable
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name IN ('session_id', 'terminal_id')
    """)
    
    rows = cur.fetchall()
    for row in rows:
        print(f"Column {row[0]} is now nullable: {row[1]}")
    
    # Close the connection
    cur.close()
    conn.close()
    print("\nMigration completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
