"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Search,
  BookOpen,
  Video,
  FileText,
  ChevronRight,
  ExternalLink,
  Clock,
  Calendar,
  Users,
  BarChart2,
  TrendingUp,
  DollarSign,
} from "lucide-react"

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Help Center</CardTitle>
            <CardDescription className="text-gray-400">
              Find answers, tutorials, and resources to help you succeed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search for help articles, tutorials, and more..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 py-6 bg-[#001a2c] border-[#003a4c] text-white text-lg"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  title: "Getting Started",
                  description: "New to Funded Whales? Start here to learn the basics.",
                  icon: <BookOpen className="h-8 w-8 text-sky-500" />,
                  articles: 12,
                },
                {
                  title: "Trading Platform",
                  description: "Learn how to use MetaTrader 5 effectively.",
                  icon: <BarChart2 className="h-8 w-8 text-sky-500" />,
                  articles: 18,
                },
                {
                  title: "Account Management",
                  description: "Manage your profile, security, and account settings.",
                  icon: <Users className="h-8 w-8 text-sky-500" />,
                  articles: 9,
                },
                {
                  title: "Trading Challenges",
                  description: "Everything you need to know about our trading challenges.",
                  icon: <TrendingUp className="h-8 w-8 text-sky-500" />,
                  articles: 15,
                },
                {
                  title: "Withdrawals & Payments",
                  description: "Learn about our withdrawal process and payment methods.",
                  icon: <DollarSign className="h-8 w-8 text-sky-500" />,
                  articles: 7,
                },
                {
                  title: "Trading Rules",
                  description: "Understand our trading rules and compliance requirements.",
                  icon: <FileText className="h-8 w-8 text-sky-500" />,
                  articles: 10,
                },
              ].map((category, index) => (
                <Card
                  key={index}
                  className="bg-[#001a2c] border-[#003a4c] hover:border-teal-500/50 transition-colors cursor-pointer"
                >
                  <CardHeader>
                    <div className="flex items-center">
                      {category.icon}
                      <CardTitle className="text-white ml-3">{category.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-400 mb-2">{category.description}</CardDescription>
                    <p className="text-sm text-teal-400">{category.articles} articles</p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="ghost" className="text-teal-400 hover:text-teal-300 hover:bg-transparent p-0">
                      Browse Articles
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Popular Articles</CardTitle>
            <CardDescription className="text-gray-400">Most frequently read help articles and guides</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  title: "How to Set Up MetaTrader 5 for Optimal Trading",
                  category: "Trading Platform",
                  readTime: "5 min read",
                  icon: <BarChart2 className="h-5 w-5 text-teal-500" />,
                },
                {
                  title: "Understanding the Two-Phase Challenge Process",
                  category: "Trading Challenges",
                  readTime: "7 min read",
                  icon: <TrendingUp className="h-5 w-5 text-teal-500" />,
                },
                {
                  title: "Complete Guide to KYC Verification",
                  category: "Account Management",
                  readTime: "4 min read",
                  icon: <Users className="h-5 w-5 text-teal-500" />,
                },
                {
                  title: "How to Request and Receive Your First Payout",
                  category: "Withdrawals & Payments",
                  readTime: "6 min read",
                  icon: <DollarSign className="h-5 w-5 text-teal-500" />,
                },
                {
                  title: "Trading Rules Explained: What You Need to Know",
                  category: "Trading Rules",
                  readTime: "8 min read",
                  icon: <FileText className="h-5 w-5 text-teal-500" />,
                },
              ].map((article, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c] hover:border-teal-500/50 transition-colors cursor-pointer"
                >
                  <div className="flex items-center">
                    {article.icon}
                    <div className="ml-3">
                      <p className="text-white font-medium">{article.title}</p>
                      <div className="flex items-center text-sm text-gray-400 mt-1">
                        <span>{article.category}</span>
                        <span className="mx-2">•</span>
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{article.readTime}</span>
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Video Tutorials</CardTitle>
              <CardDescription className="text-gray-400">
                Learn visually with our step-by-step video guides
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    title: "Getting Started with Funded Whales",
                    duration: "12:34",
                    date: "May 1, 2025",
                  },
                  {
                    title: "How to Use MetaTrader 5 for Beginners",
                    duration: "18:22",
                    date: "Apr 25, 2025",
                  },
                  {
                    title: "Advanced Risk Management Strategies",
                    duration: "22:15",
                    date: "Apr 18, 2025",
                  },
                ].map((video, index) => (
                  <div
                    key={index}
                    className="flex items-center p-4 border border-[#003a4c] rounded-lg bg-[#001a2c] hover:border-teal-500/50 transition-colors cursor-pointer"
                  >
                    <div className="h-16 w-28 bg-[#00121c] rounded-md flex items-center justify-center mr-4 relative">
                      <Video className="h-6 w-6 text-teal-500" />
                      <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                        {video.duration}
                      </div>
                    </div>
                    <div>
                      <p className="text-white font-medium">{video.title}</p>
                      <div className="flex items-center text-sm text-gray-400 mt-1">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>{video.date}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4 border-[#003a4c] text-white hover:bg-[#003a4c]">
                View All Videos
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Upcoming Webinars</CardTitle>
              <CardDescription className="text-gray-400">
                Join our live educational sessions with trading experts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    title: "Mastering Price Action Trading",
                    presenter: "John Smith, Senior Trader",
                    date: "May 15, 2025",
                    time: "2:00 PM ET",
                  },
                  {
                    title: "Risk Management for Consistent Profits",
                    presenter: "Sarah Johnson, Risk Analyst",
                    date: "May 22, 2025",
                    time: "3:30 PM ET",
                  },
                  {
                    title: "Advanced MetaTrader 5 Features",
                    presenter: "Michael Brown, Platform Specialist",
                    date: "May 29, 2025",
                    time: "1:00 PM ET",
                  },
                ].map((webinar, index) => (
                  <div
                    key={index}
                    className="p-4 border border-[#003a4c] rounded-lg bg-[#001a2c] hover:border-teal-500/50 transition-colors cursor-pointer"
                  >
                    <p className="text-white font-medium">{webinar.title}</p>
                    <p className="text-sm text-gray-400 mt-1">{webinar.presenter}</p>
                    <div className="flex items-center text-sm text-teal-400 mt-2">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{webinar.date}</span>
                      <span className="mx-2">•</span>
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{webinar.time}</span>
                    </div>
                    <Button variant="outline" size="sm" className="mt-3 border-[#003a4c] text-white hover:bg-[#003a4c]">
                      Register
                    </Button>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4 border-[#003a4c] text-white hover:bg-[#003a4c]">
                View All Webinars
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.5 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Additional Resources</CardTitle>
            <CardDescription className="text-gray-400">
              Explore more learning materials and trading resources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col p-6 bg-[#001a2c] rounded-lg border border-[#003a4c] h-full">
                <BookOpen className="h-10 w-10 text-teal-500 mb-4" />
                <h3 className="text-white font-medium mb-2">Trading Academy</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Comprehensive courses for traders of all levels, from beginners to advanced.
                </p>
                <Button
                  variant="outline"
                  className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c] justify-between"
                >
                  <span>Browse Courses</span>
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
              </div>

              <div className="flex flex-col p-6 bg-[#001a2c] rounded-lg border border-[#003a4c] h-full">
                <FileText className="h-10 w-10 text-teal-500 mb-4" />
                <h3 className="text-white font-medium mb-2">E-Books & Guides</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Download our collection of trading e-books, guides, and checklists.
                </p>
                <Button
                  variant="outline"
                  className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c] justify-between"
                >
                  <span>View Library</span>
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
              </div>

              <div className="flex flex-col p-6 bg-[#001a2c] rounded-lg border border-[#003a4c] h-full">
                <Users className="h-10 w-10 text-teal-500 mb-4" />
                <h3 className="text-white font-medium mb-2">Community Forum</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Connect with other traders, share strategies, and learn from the community.
                </p>
                <Button
                  variant="outline"
                  className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c] justify-between"
                >
                  <span>Join Discussion</span>
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              Can't find what you're looking for? Contact our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}
