import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

print(f"Using database URL: {database_url}")

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor
    cur = conn.cursor()
    
    # Tables and columns to check
    tables_columns = [
        ("liveaccount", ["session_id", "terminal_id"]),
        ("stage2account", ["session_id", "terminal_id"])
    ]
    
    # Check if columns are nullable
    for table, columns in tables_columns:
        print(f"\nChecking columns in {table} table:")
        for column in columns:
            cur.execute(f"""
                SELECT column_name, is_nullable, data_type
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}'
            """)
            
            result = cur.fetchone()
            if result:
                print(f"  Column {result[0]}: nullable={result[1]}, type={result[2]}")
            else:
                print(f"  Column {column} not found")
    
    # Close the connection
    cur.close()
    conn.close()
    print("\nDatabase connection closed")
    
except Exception as e:
    print(f"Error: {str(e)}")
    if 'conn' in locals() and conn:
        conn.close()
