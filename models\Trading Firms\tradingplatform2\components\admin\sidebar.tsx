"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  BarChart3,
  DollarSign,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Settings,
  ShieldCheck,
  Users,
  X,
  ClipboardList,
  Award,
  TrendingUp,
  CheckCircle,
  Clock,
  Play,
  XCircle,
} from "lucide-react"

interface AdminSidebarProps {
  className?: string
}

export default function AdminSidebar({ className }: AdminSidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  const routes = [
    {
      label: "Admin Dashboard",
      icon: Home,
      href: "/admin",
      active: pathname === "/admin",
    },
    {
      label: "User Management",
      icon: Users,
      href: "/admin/users",
      active: pathname === "/admin/users",
    },
    {
      label: "All Orders",
      icon: ClipboardList,
      href: "/admin/orders",
      active: pathname === "/admin/orders",
    },
    {
      label: "Running Orders",
      icon: Play,
      href: "/admin/orders/running",
      active: pathname === "/admin/orders/running",
    },
    {
      label: "Completed Orders",
      icon: CheckCircle,
      href: "/admin/orders/completed",
      active: pathname === "/admin/orders/completed",
    },
    {
      label: "Passed Orders",
      icon: TrendingUp,
      href: "/admin/orders/passed",
      active: pathname === "/admin/orders/passed",
    },
    {
      label: "Failed Orders",
      icon: XCircle,
      href: "/admin/orders/failed",
      active: pathname === "/admin/orders/failed",
    },
    {
      label: "Stage 2 Orders",
      icon: BarChart3,
      href: "/admin/orders/stage2",
      active: pathname === "/admin/orders/stage2",
    },
    {
      label: "Live Orders",
      icon: Clock,
      href: "/admin/orders/live",
      active: pathname === "/admin/orders/live",
    },
    {
      label: "Certificates",
      icon: Award,
      href: "/admin/certificates",
      active: pathname === "/admin/certificates",
    },
    {
      label: "KYC Verification",
      icon: ShieldCheck,
      href: "/admin/kyc",
      active: pathname === "/admin/kyc",
    },
    {
      label: "Withdrawals",
      icon: DollarSign,
      href: "/admin/withdrawals",
      active: pathname === "/admin/withdrawals",
    },
    {
      label: "Support Tickets",
      icon: MessageSquare,
      href: "/admin/support",
      active: pathname === "/admin/support",
    },
    {
      label: "Settings",
      icon: Settings,
      href: "/admin/settings",
      active: pathname === "/admin/settings",
    },
  ]

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && <div className="fixed inset-0 z-40 bg-black/80 lg:hidden" onClick={toggleMobileSidebar} />}

      {/* Mobile Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 lg:hidden text-white"
        onClick={toggleMobileSidebar}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 flex flex-col bg-[#001a2c] border-r border-[#003a4c] transition-all duration-300 ease-in-out",
          isCollapsed ? "w-[70px]" : "w-[280px]",
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className,
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-[#003a4c]">
          <Link href="/admin" className="flex items-center">
            {!isCollapsed && (
              <span className="text-xl font-bold text-white">
                ADMIN <span className="text-sky-400">PANEL</span>
              </span>
            )}
            {isCollapsed && <span className="text-xl font-bold text-sky-400">AP</span>}
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="hidden lg:flex text-gray-400 hover:text-white"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto py-4 px-3">
          <nav className="space-y-1">
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  route.active ? "bg-[#003a4c] text-white" : "text-gray-400 hover:text-white hover:bg-[#002a3c]",
                )}
              >
                <route.icon className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
                {!isCollapsed && <span>{route.label}</span>}
              </Link>
            ))}
          </nav>
        </div>

        <div className="p-4 border-t border-[#003a4c]">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-gray-400 hover:text-white hover:bg-[#002a3c]",
              isCollapsed && "justify-center",
            )}
          >
            <LogOut className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
            {!isCollapsed && <span>Logout</span>}
          </Button>
        </div>
      </div>
    </>
  )
}
