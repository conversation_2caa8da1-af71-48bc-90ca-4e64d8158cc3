#!/usr/bin/env python3
"""
<PERSON>ript to run the migration that adds referral columns to the user table
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def run_migration():
    """Run the migration to add referral columns"""
    try:
        print("Running migration to add referral columns...")
        
        # Import and run the migration
        from migrate_add_created_at import migrate_add_user_referral_columns
        
        migrate_add_user_referral_columns()
        
        print("✅ Migration completed successfully!")
        print("The following columns have been added to the user table:")
        print("  - referral_code (VARCHAR, UNIQUE)")
        print("  - referred_by (VARCHAR)")
        print("  - total_points (FLOAT, DEFAULT 0)")
        print("\nExisting users have been assigned unique referral codes.")
        
    except Exception as e:
        print(f"❌ Error running migration: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_migration()
