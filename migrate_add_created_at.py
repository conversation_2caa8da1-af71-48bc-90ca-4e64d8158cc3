import os
import psycopg2
from dotenv import load_dotenv
import uuid

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def generate_referral_code():
    return str(uuid.uuid4())[:8].upper()

def migrate_add_user_referral_columns():
    """
    Migration script to add referral_code, referred_by, and total_points columns to user table
    """
    print("Starting migration to add referral columns to user table...")

    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()

    try:
        # Check and add referral_code column
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='user' AND column_name='referral_code';
        """)

        if cursor.fetchone() is None:
            print("Column 'referral_code' does not exist. Adding it...")

            # Add the referral_code column
            cursor.execute("""
                ALTER TABLE "user"
                ADD COLUMN referral_code VARCHAR;
            """)

            # Add unique constraint
            cursor.execute("""
                ALTER TABLE "user"
                ADD CONSTRAINT user_referral_code_unique UNIQUE (referral_code);
            """)

            print("Column 'referral_code' added successfully.")
        else:
            print("Column 'referral_code' already exists.")

        # Check and add referred_by column
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='user' AND column_name='referred_by';
        """)

        if cursor.fetchone() is None:
            print("Column 'referred_by' does not exist. Adding it...")

            # Add the referred_by column
            cursor.execute("""
                ALTER TABLE "user"
                ADD COLUMN referred_by VARCHAR;
            """)

            print("Column 'referred_by' added successfully.")
        else:
            print("Column 'referred_by' already exists.")

        # Check and add total_points column
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='user' AND column_name='total_points';
        """)

        if cursor.fetchone() is None:
            print("Column 'total_points' does not exist. Adding it...")

            # Add the total_points column with default value
            cursor.execute("""
                ALTER TABLE "user"
                ADD COLUMN total_points FLOAT DEFAULT 0;
            """)

            print("Column 'total_points' added successfully.")
        else:
            print("Column 'total_points' already exists.")

        # Update existing users to have referral codes if they don't have them
        cursor.execute("""
            SELECT id FROM "user" WHERE referral_code IS NULL;
        """)

        users_without_codes = cursor.fetchall()
        if users_without_codes:
            print(f"Updating {len(users_without_codes)} users with referral codes...")

            for user_row in users_without_codes:
                user_id = user_row[0]
                new_referral_code = generate_referral_code()

                # Keep generating until we get a unique code
                while True:
                    cursor.execute("""
                        SELECT id FROM "user" WHERE referral_code = %s;
                    """, (new_referral_code,))

                    if cursor.fetchone() is None:
                        break
                    new_referral_code = generate_referral_code()

                cursor.execute("""
                    UPDATE "user" SET referral_code = %s WHERE id = %s;
                """, (new_referral_code, user_id))

            print("All existing users updated with referral codes.")

        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def migrate_add_created_at():
    """
    Migration script to add created_at column to ordermodel table
    """
    print("Starting migration to add created_at column to ordermodel table...")

    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()

    try:
        # Check if the column already exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='ordermodel' AND column_name='created_at';
        """)

        if cursor.fetchone() is None:
            print("Column 'created_at' does not exist. Adding it...")

            # Add the created_at column with a default value of current timestamp
            cursor.execute("""
                ALTER TABLE ordermodel
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """)

            print("Column 'created_at' added successfully.")
        else:
            print("Column 'created_at' already exists. No changes needed.")

        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    migrate_add_user_referral_columns()
    migrate_add_created_at()
