from sqlmodel import SQLModel, Field, Relationship
from datetime import datetime
from typing import Optional, List  # Keep the import for Optional
from pydantic import EmailStr
import uuid

def generate_referral_code():
    return str(uuid.uuid4())[:8].upper()

class User(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    username: str
    email: EmailStr
    hashed_password: str 
    name: str
    phone_no: str
    country: str 
    address: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    referral_code: str = Field(default_factory=generate_referral_code, unique=True)
    referred_by: Optional[str] = Field(default=None)  # Referral code of the user who referred this user
    total_points: float = Field(default=0)
    
    # Relationships
    referral_points: List["ReferralPoints"] = Relationship(back_populates="user")
    withdrawal_requests: List["WithdrawalRequest"] = Relationship(back_populates="user")
    giveaway_entries: List["GiveawayEntry"] = Relationship(back_populates="user")
     