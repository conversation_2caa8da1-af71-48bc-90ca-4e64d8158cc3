import os
import sys
from fastapi import FastAPI
import uvicorn

# Create a simple FastAPI app
app = FastAPI(title="FXentra Trading Platform")

@app.get("/")
async def root():
    return {"message": "Welcome to FXentra Trading Platform API"}

# Run the app if executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("app:app", host="0.0.0.0", port=port)
