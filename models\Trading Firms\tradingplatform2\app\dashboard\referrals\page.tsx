"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Copy, DollarSign, Gift, Share2, Users } from "lucide-react"

export default function ReferralsPage() {
  const [copied, setCopied] = useState(false)
  const referralLink = "https://fundedwhales.com/ref/johndoe123"
  const referralCode = "JOHNDOE123"

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      {/* Referral Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          {
            title: "Total Referrals",
            value: "24",
            icon: <Users className="h-5 w-5 text-sky-500" />,
            description: "People who signed up",
          },
          {
            title: "Active Referrals",
            value: "18",
            icon: <Users className="h-5 w-5 text-sky-500" />,
            description: "Purchased a challenge",
          },
          {
            title: "Total Earnings",
            value: "$1,250",
            icon: <DollarSign className="h-5 w-5 text-sky-500" />,
            description: "Lifetime earnings",
          },
          {
            title: "Available Rewards",
            value: "$350",
            icon: <Gift className="h-5 w-5 text-sky-500" />,
            description: "Ready to withdraw",
          },
        ].map((stat, index) => (
          <motion.div
            key={index}
            variants={fadeInUp}
            initial="hidden"
            animate="visible"
            transition={{ delay: index * 0.1 }}
          >
            <Card className="bg-[#002a3c] border-[#003a4c]">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-gray-400">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <p className="text-xs text-gray-400">{stat.description}</p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Referral Program */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          className="lg:col-span-2"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Your Referral Link</CardTitle>
              <CardDescription className="text-gray-400">
                Share this link with friends to earn rewards
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex">
                  <Input
                    value={referralLink}
                    readOnly
                    className="bg-[#001a2c] border-[#003a4c] text-white rounded-r-none"
                  />
                  <Button
                    className="bg-teal-500 hover:bg-teal-600 text-white rounded-l-none"
                    onClick={() => copyToClipboard(referralLink)}
                  >
                    {copied ? "Copied!" : "Copy"}
                  </Button>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Your referral code: {referralCode}</span>
                  <button
                    className="text-sm text-teal-400 hover:underline flex items-center"
                    onClick={() => copyToClipboard(referralCode)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy code
                  </button>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <Button className="bg-[#1877F2] hover:bg-[#1877F2]/90 text-white">
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9.19795 21.5H13.198V13.4901H16.8021L17.198 9.50977H13.198V7.5C13.198 6.94772 13.6457 6.5 14.198 6.5H17.198V2.5H14.198C11.4365 2.5 9.19795 4.73858 9.19795 7.5V9.50977H7.19795L6.80206 13.4901H9.19795V21.5Z" />
                  </svg>
                  Share on Facebook
                </Button>
                <Button className="bg-[#1DA1F2] hover:bg-[#1DA1F2]/90 text-white">
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                  </svg>
                  Share on Twitter
                </Button>
                <Button className="bg-[#25D366] hover:bg-[#25D366]/90 text-white">
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
                  </svg>
                  Share on WhatsApp
                </Button>
                <Button className="bg-[#0088CC] hover:bg-[#0088CC]/90 text-white">
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
                  </svg>
                  Share on Telegram
                </Button>
                <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                  <Share2 className="mr-2 h-4 w-4" />
                  More Options
                </Button>
              </div>

              <div className="pt-4 border-t border-[#003a4c]">
                <h3 className="text-white font-medium mb-4">Referral Program Benefits</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-[#001a2c] rounded-lg p-4 border border-[#003a4c]">
                    <h4 className="text-teal-400 font-medium mb-2">For You</h4>
                    <ul className="space-y-2 text-sm text-gray-400">
                      <li className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>Earn 10% of your referral's first purchase</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>5% recurring commission on all future purchases</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>Bonus rewards for high-performing referrers</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-[#001a2c] rounded-lg p-4 border border-[#003a4c]">
                    <h4 className="text-teal-400 font-medium mb-2">For Your Friends</h4>
                    <ul className="space-y-2 text-sm text-gray-400">
                      <li className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>10% discount on their first challenge purchase</span>
                      </li>
                      <li className="flex items\
