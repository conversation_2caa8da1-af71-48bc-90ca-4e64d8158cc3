import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def check_user_table_schema():
    """
    Check the current schema of the user table
    """
    print("Checking user table schema...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    cursor = conn.cursor()
    
    try:
        # Get all columns for the user table
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name='user'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        
        print(f"\nUser table has {len(columns)} columns:")
        print("-" * 80)
        print(f"{'Column Name':<20} {'Data Type':<15} {'Nullable':<10} {'Default':<20}")
        print("-" * 80)
        
        for column in columns:
            column_name, data_type, is_nullable, column_default = column
            default_str = str(column_default) if column_default else "None"
            if len(default_str) > 18:
                default_str = default_str[:15] + "..."
            print(f"{column_name:<20} {data_type:<15} {is_nullable:<10} {default_str:<20}")
        
        # Check specifically for the referral columns
        referral_columns = ['referral_code', 'referred_by', 'total_points']
        print(f"\nChecking for referral columns:")
        for col in referral_columns:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name='user' AND column_name=%s;
            """, (col,))
            
            result = cursor.fetchone()
            if result:
                print(f"✓ {col}: {result[1]} (nullable: {result[2]}, default: {result[3]})")
            else:
                print(f"✗ {col}: NOT FOUND")
        
        # Check for unique constraints
        print(f"\nChecking unique constraints on user table:")
        cursor.execute("""
            SELECT constraint_name, column_name
            FROM information_schema.key_column_usage
            WHERE table_name = 'user' AND constraint_name LIKE '%unique%';
        """)
        
        constraints = cursor.fetchall()
        for constraint in constraints:
            print(f"✓ Unique constraint: {constraint[0]} on column {constraint[1]}")
        
    except Exception as e:
        print(f"Error checking schema: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_user_table_schema()
