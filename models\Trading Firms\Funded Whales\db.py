from sqlmodel import SQLModel, Session, create_engine
from sqlalchemy.pool import <PERSON>ull<PERSON><PERSON>
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Create the SQLAlchemy engine
engine = create_engine(
    database_url,
    echo=True,
    poolclass=NullPool  # Prevents connection issues
    # Removed statement_timeout as it's not supported by Neon's connection pooler
)

def create_db_and_tables():
    """Create database tables based on SQLModel definitions."""
    # Import all models to ensure they're registered with SQLModel metadata
    # This import is used to register the model with SQLModel metadata
    # pylint: disable=unused-import
    from routes.account import AccountCredential

    # Create all tables
    SQLModel.metadata.create_all(engine)

    # Explicitly create the account_credentials table
    try:
        AccountCredential.create_table(engine)
        print("Ensured account_credentials table exists")
    except Exception as e:
        print(f"Error ensuring account_credentials table: {str(e)}")

    # Add created_at column to ordermodel table if it doesn't exist
    try:
        with Session(engine) as session:
            # Check if the column exists
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name='ordermodel' AND column_name='created_at';
            """))

            if not result.fetchone():
                print("Adding created_at column to ordermodel table...")
                session.execute(text("""
                    ALTER TABLE ordermodel
                    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                """))
                session.commit()
                print("created_at column added successfully.")
            else:
                print("created_at column already exists in ordermodel table.")
    except Exception as e:
        print(f"Error checking/adding created_at column: {str(e)}")

def get_session():
    """Provide a database session using a context manager."""
    with Session(engine) as session:
        yield session
