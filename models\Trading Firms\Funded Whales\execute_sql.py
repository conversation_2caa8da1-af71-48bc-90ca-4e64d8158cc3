import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor and set autocommit to True
    conn.autocommit = True
    cur = conn.cursor()
    
    # Read the SQL script
    with open("update_db_schema.sql", "r") as f:
        sql_script = f.read()
    
    # Execute the SQL script
    print("Executing SQL script...")
    cur.execute(sql_script)
    print("SQL script executed successfully")
    
    # Verify the changes
    print("\nVerifying changes...")
    tables_columns = [
        ("liveaccount", ["session_id", "terminal_id"]),
        ("stage2account", ["session_id", "terminal_id"])
    ]
    
    for table, columns in tables_columns:
        for column in columns:
            cur.execute(f"""
                SELECT column_name, is_nullable
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}'
            """)
            
            result = cur.fetchone()
            if result:
                print(f"Column {result[0]} in {table} is now nullable: {result[1]}")
            else:
                print(f"Column {column} in {table} not found")
    
    # Close the connection
    cur.close()
    conn.close()
    print("\nDatabase schema update completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
    if 'conn' in locals() and conn:
        conn.close()
