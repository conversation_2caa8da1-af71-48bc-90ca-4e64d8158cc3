import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

print(f"Using database URL: {database_url}")

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor
    cur = conn.cursor()
    
    # Set autocommit to True
    conn.autocommit = True
    
    # SQL commands to make session_id and terminal_id nullable
    sql_commands = [
        "ALTER TABLE liveaccount ALTER COLUMN session_id DROP NOT NULL;",
        "ALTER TABLE liveaccount ALTER COLUMN terminal_id DROP NOT NULL;",
        "ALTER TABLE stage2account ALTER COLUMN session_id DROP NOT NULL;",
        "ALTER TABLE stage2account ALTER COLUMN terminal_id DROP NOT NULL;"
    ]
    
    # Execute each SQL command
    for sql in sql_commands:
        try:
            print(f"Executing: {sql}")
            cur.execute(sql)
            print("Command executed successfully")
        except Exception as e:
            print(f"Error executing command: {str(e)}")
            # Continue with the next command
    
    # Close the connection
    cur.close()
    conn.close()
    print("Database connection closed")
    
except Exception as e:
    print(f"Error: {str(e)}")
    if 'conn' in locals() and conn:
        conn.close()
