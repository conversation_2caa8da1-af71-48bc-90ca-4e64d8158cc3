import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def migrate_add_order_columns():
    """
    Migration script to add missing columns to order-related tables
    """
    print("Starting migration to add missing columns to order tables...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    
    try:
        # Add session_id column to completeordermodel table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='completeordermodel' AND column_name='session_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'session_id' does not exist in completeordermodel. Adding it...")
            
            cursor.execute("""
                ALTER TABLE completeordermodel 
                ADD COLUMN session_id VARCHAR;
            """)
            
            print("Column 'session_id' added successfully to completeordermodel.")
        else:
            print("Column 'session_id' already exists in completeordermodel.")
        
        # Add terminal_id column to completeordermodel table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='completeordermodel' AND column_name='terminal_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'terminal_id' does not exist in completeordermodel. Adding it...")
            
            cursor.execute("""
                ALTER TABLE completeordermodel 
                ADD COLUMN terminal_id INTEGER;
            """)
            
            print("Column 'terminal_id' added successfully to completeordermodel.")
        else:
            print("Column 'terminal_id' already exists in completeordermodel.")
        
        # Add profit_target column to completeordermodel table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='completeordermodel' AND column_name='profit_target';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'profit_target' does not exist in completeordermodel. Adding it...")
            
            cursor.execute("""
                ALTER TABLE completeordermodel 
                ADD COLUMN profit_target INTEGER;
            """)
            
            print("Column 'profit_target' added successfully to completeordermodel.")
        else:
            print("Column 'profit_target' already exists in completeordermodel.")
        
        # Add completed_at column to completeordermodel table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='completeordermodel' AND column_name='completed_at';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'completed_at' does not exist in completeordermodel. Adding it...")
            
            cursor.execute("""
                ALTER TABLE completeordermodel 
                ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """)
            
            print("Column 'completed_at' added successfully to completeordermodel.")
        else:
            print("Column 'completed_at' already exists in completeordermodel.")
        
        # Check and add session_id to stage2account table if needed
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='stage2account' AND column_name='session_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'session_id' does not exist in stage2account. Adding it...")
            
            cursor.execute("""
                ALTER TABLE stage2account 
                ADD COLUMN session_id VARCHAR;
            """)
            
            print("Column 'session_id' added successfully to stage2account.")
        else:
            print("Column 'session_id' already exists in stage2account.")
        
        # Check and add terminal_id to stage2account table if needed
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='stage2account' AND column_name='terminal_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'terminal_id' does not exist in stage2account. Adding it...")
            
            cursor.execute("""
                ALTER TABLE stage2account 
                ADD COLUMN terminal_id INTEGER;
            """)
            
            print("Column 'terminal_id' added successfully to stage2account.")
        else:
            print("Column 'terminal_id' already exists in stage2account.")
        
        # Check and add session_id to liveaccount table if needed
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='liveaccount' AND column_name='session_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'session_id' does not exist in liveaccount. Adding it...")
            
            cursor.execute("""
                ALTER TABLE liveaccount 
                ADD COLUMN session_id VARCHAR;
            """)
            
            print("Column 'session_id' added successfully to liveaccount.")
        else:
            print("Column 'session_id' already exists in liveaccount.")
        
        # Check and add terminal_id to liveaccount table if needed
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='liveaccount' AND column_name='terminal_id';
        """)
        
        if cursor.fetchone() is None:
            print("Column 'terminal_id' does not exist in liveaccount. Adding it...")
            
            cursor.execute("""
                ALTER TABLE liveaccount 
                ADD COLUMN terminal_id INTEGER;
            """)
            
            print("Column 'terminal_id' added successfully to liveaccount.")
        else:
            print("Column 'terminal_id' already exists in liveaccount.")
        
        print("Migration completed successfully.")
    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    migrate_add_order_columns()
