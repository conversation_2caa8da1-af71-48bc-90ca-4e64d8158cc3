-- Heroku Migration Script to Fix Order Table Columns
-- Run this SQL script on your Heroku PostgreSQL database

-- Fix completeordermodel table
DO $$
BEGIN
    -- Add completed_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name='completed_at'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added completed_at column to completeordermodel';
    ELSE
        RAISE NOTICE 'completed_at column already exists in completeordermodel';
    END IF;
    
    -- Add profit_target column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name='profit_target'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN profit_target INTEGER;
        RAISE NOTICE 'Added profit_target column to completeordermodel';
    ELSE
        RAISE NOTICE 'profit_target column already exists in completeordermodel';
    END IF;
    
    -- Add session_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name='session_id'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN session_id VARCHAR;
        RAISE NOTICE 'Added session_id column to completeordermodel';
    ELSE
        RAISE NOTICE 'session_id column already exists in completeordermodel';
    END IF;
    
    -- Add terminal_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name='terminal_id'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN terminal_id INTEGER;
        RAISE NOTICE 'Added terminal_id column to completeordermodel';
    ELSE
        RAISE NOTICE 'terminal_id column already exists in completeordermodel';
    END IF;
END $$;

-- Fix liveaccount table
DO $$
BEGIN
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='created_at'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to liveaccount';
    ELSE
        RAISE NOTICE 'created_at column already exists in liveaccount';
    END IF;
    
    -- Add profit_share column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='profit_share'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN profit_share FLOAT DEFAULT 80.0;
        RAISE NOTICE 'Added profit_share column to liveaccount';
    ELSE
        RAISE NOTICE 'profit_share column already exists in liveaccount';
    END IF;
    
    -- Add status column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='status'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN status VARCHAR DEFAULT 'active';
        RAISE NOTICE 'Added status column to liveaccount';
    ELSE
        RAISE NOTICE 'status column already exists in liveaccount';
    END IF;
    
    -- Add session_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='session_id'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN session_id VARCHAR;
        RAISE NOTICE 'Added session_id column to liveaccount';
    ELSE
        RAISE NOTICE 'session_id column already exists in liveaccount';
    END IF;
    
    -- Add terminal_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='terminal_id'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN terminal_id INTEGER;
        RAISE NOTICE 'Added terminal_id column to liveaccount';
    ELSE
        RAISE NOTICE 'terminal_id column already exists in liveaccount';
    END IF;
END $$;

-- Fix stage2account table
DO $$
BEGIN
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='created_at'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to stage2account';
    ELSE
        RAISE NOTICE 'created_at column already exists in stage2account';
    END IF;
    
    -- Add profit_target column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='profit_target'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN profit_target FLOAT;
        RAISE NOTICE 'Added profit_target column to stage2account';
    ELSE
        RAISE NOTICE 'profit_target column already exists in stage2account';
    END IF;
    
    -- Add status column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='status'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN status VARCHAR DEFAULT 'active';
        RAISE NOTICE 'Added status column to stage2account';
    ELSE
        RAISE NOTICE 'status column already exists in stage2account';
    END IF;
    
    -- Add session_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='session_id'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN session_id VARCHAR;
        RAISE NOTICE 'Added session_id column to stage2account';
    ELSE
        RAISE NOTICE 'session_id column already exists in stage2account';
    END IF;
    
    -- Add terminal_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='terminal_id'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN terminal_id INTEGER;
        RAISE NOTICE 'Added terminal_id column to stage2account';
    ELSE
        RAISE NOTICE 'terminal_id column already exists in stage2account';
    END IF;
END $$;

-- Fix ordermodel table
DO $$
BEGIN
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='ordermodel' AND column_name='created_at'
    ) THEN
        ALTER TABLE ordermodel ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to ordermodel';
    ELSE
        RAISE NOTICE 'created_at column already exists in ordermodel';
    END IF;
END $$;

-- Fix orderimage table
DO $$
BEGIN
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='orderimage' AND column_name='created_at'
    ) THEN
        ALTER TABLE orderimage ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to orderimage';
    ELSE
        RAISE NOTICE 'created_at column already exists in orderimage';
    END IF;
END $$;

-- Fix passorder table
DO $$
BEGIN
    -- Add pass_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='passorder' AND column_name='pass_date'
    ) THEN
        ALTER TABLE passorder ADD COLUMN pass_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added pass_date column to passorder';
    ELSE
        RAISE NOTICE 'pass_date column already exists in passorder';
    END IF;
    
    -- Add profit_amount column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='passorder' AND column_name='profit_amount'
    ) THEN
        ALTER TABLE passorder ADD COLUMN profit_amount FLOAT;
        RAISE NOTICE 'Added profit_amount column to passorder';
    ELSE
        RAISE NOTICE 'profit_amount column already exists in passorder';
    END IF;
    
    -- Add notes column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='passorder' AND column_name='notes'
    ) THEN
        ALTER TABLE passorder ADD COLUMN notes VARCHAR;
        RAISE NOTICE 'Added notes column to passorder';
    ELSE
        RAISE NOTICE 'notes column already exists in passorder';
    END IF;
END $$;

-- Fix ordertimeline table
DO $$
BEGIN
    -- Add event_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='ordertimeline' AND column_name='event_date'
    ) THEN
        ALTER TABLE ordertimeline ADD COLUMN event_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added event_date column to ordertimeline';
    ELSE
        RAISE NOTICE 'event_date column already exists in ordertimeline';
    END IF;
    
    -- Add notes column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='ordertimeline' AND column_name='notes'
    ) THEN
        ALTER TABLE ordertimeline ADD COLUMN notes VARCHAR;
        RAISE NOTICE 'Added notes column to ordertimeline';
    ELSE
        RAISE NOTICE 'notes column already exists in ordertimeline';
    END IF;
END $$;

-- Fix rejectorder table
DO $$
BEGIN
    -- Add rejected_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='rejectorder' AND column_name='rejected_at'
    ) THEN
        ALTER TABLE rejectorder ADD COLUMN rejected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added rejected_at column to rejectorder';
    ELSE
        RAISE NOTICE 'rejected_at column already exists in rejectorder';
    END IF;
END $$;

-- Fix failorder table
DO $$
BEGIN
    -- Add failed_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='failorder' AND column_name='failed_at'
    ) THEN
        ALTER TABLE failorder ADD COLUMN failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added failed_at column to failorder';
    ELSE
        RAISE NOTICE 'failed_at column already exists in failorder';
    END IF;
END $$;

-- Fix certificate table
DO $$
BEGIN
    -- Add issue_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='certificate' AND column_name='issue_date'
    ) THEN
        ALTER TABLE certificate ADD COLUMN issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added issue_date column to certificate';
    ELSE
        RAISE NOTICE 'issue_date column already exists in certificate';
    END IF;
    
    -- Add profit_target column if it doesn't exist
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='certificate' AND column_name='profit_target'
    ) THEN
        ALTER TABLE certificate ADD COLUMN profit_target FLOAT;
        RAISE NOTICE 'Added profit_target column to certificate';
    ELSE
        RAISE NOTICE 'profit_target column already exists in certificate';
    END IF;
END $$;

-- Verify the migration
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('completeordermodel', 'liveaccount', 'stage2account', 'ordermodel', 'orderimage', 'passorder', 'ordertimeline', 'rejectorder', 'failorder', 'certificate')
AND column_name IN ('completed_at', 'created_at', 'pass_date', 'event_date', 'rejected_at', 'failed_at', 'issue_date', 'profit_target', 'profit_share', 'status', 'session_id', 'terminal_id', 'profit_amount', 'notes')
ORDER BY table_name, column_name; 