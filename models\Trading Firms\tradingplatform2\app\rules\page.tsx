import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { AlertCircle, CheckCircle, XCircle } from "lucide-react"

export default function RulesPage() {
  const generalRules = [
    "Minimum trading days: 1 day",
    "Maximum daily loss: 5% of initial balance",
    "Maximum total loss: 10% of initial balance",
    "Profit target: 8% of initial balance",
    "Minimum trading days to reach profit target: 1 day",
    "Maximum trading days to reach profit target: No limit",
  ]

  const allowedStrategies = [
    "Scalping",
    "Day trading",
    "Swing trading",
    "Position trading",
    "News trading (with proper risk management)",
    "Technical analysis",
    "Fundamental analysis",
  ]

  const prohibitedStrategies = [
    "Martingale or grid trading",
    "Arbitrage",
    "Hedging on the same instrument",
    "Expert Advisors without prior approval",
    "Trading during major economic releases (first 10 minutes)",
    "Holding positions over weekends without sufficient margin",
    "Trading with external signals without disclosure",
  ]

  return (
    <main className="flex-1 py-20 bg-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">Trading Rules</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Our trading rules are designed to promote responsible trading and risk management. Please review them
            carefully before starting your challenge.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <AlertCircle className="mr-2 h-6 w-6 text-yellow-500" />
                General Rules
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {generalRules.map((rule, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <CheckCircle className="mr-2 h-6 w-6 text-green-500" />
                Allowed Strategies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {allowedStrategies.map((strategy, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 shrink-0" />
                    <span>{strategy}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <XCircle className="mr-2 h-6 w-6 text-red-500" />
                Prohibited Strategies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {prohibitedStrategies.map((strategy, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <XCircle className="h-5 w-5 text-red-500 mr-2 shrink-0" />
                    <span>{strategy}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-white mb-2">Trading Hours</h3>
                <p className="text-gray-400">
                  Trading is allowed 24/5, from Sunday 5:00 PM ET to Friday 5:00 PM ET. Trading during weekends is not
                  allowed except for cryptocurrencies.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-2">Account Metrics</h3>
                <p className="text-gray-400">
                  Your account metrics are calculated based on the closing prices of your trades. Unrealized profits and
                  losses are not counted towards your profit target or maximum loss limits.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-2">Violation Consequences</h3>
                <p className="text-gray-400">
                  Minor rule violations may result in warnings. Repeated or severe violations may result in account
                  termination without refund. We reserve the right to refuse service to anyone who violates our trading
                  rules.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium text-white mb-2">Rule Changes</h3>
                <p className="text-gray-400">
                  We reserve the right to modify these rules at any time. Any changes will be communicated to all
                  traders via email and will be effective immediately upon posting on our website.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
