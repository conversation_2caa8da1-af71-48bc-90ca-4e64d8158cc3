# Referral Orders Endpoints

This document describes the new endpoints for tracking orders created by users who registered via referral.

## Overview

The referral orders endpoints allow you to:
- Track all orders created by users who registered using a referral code
- View order details including account size, account type, and referral information
- Monitor referral program effectiveness

## Endpoints

### 1. Get My Referral Orders
**GET** `/referral/referral-orders`

Get all orders created by users who registered using your referral code.

**Authentication:** Required (JWT token)

**Response:**
```json
{
  "total_orders": 2,
  "orders": [
    {
      "order_id": 123,
      "account_size": "50K",
      "account_type": "Challenge",
      "referral_user_code": "ABC12345",
      "referred_user_email": "<EMAIL>",
      "order_created_at": "2024-01-15T10:30:00"
    }
  ]
}
```

### 2. Get All Referral Orders (Admin)
**GET** `/referral/admin/all-referral-orders`

Get all referral orders across the entire system (admin endpoint).

**Authentication:** Not required (public endpoint)

**Response:** Same format as above

### 3. Get Referral Orders by Code
**GET** `/referral/referral-orders/{referral_code}`

Get all orders created by users who registered using a specific referral code.

**Parameters:**
- `referral_code` (string): The referral code to search for

**Response:** Same format as above

**Error Responses:**
- `404 Not Found`: If the referral code doesn't exist

## Data Fields

### ReferralOrderResponse
- `order_id` (int): Unique identifier for the order
- `account_size` (string): Size of the trading account (e.g., "50K", "100K")
- `account_type` (string): Type of account (e.g., "Challenge", "Stage2", "Live")
- `referral_user_code` (string): Referral code of the user who referred this user
- `referred_user_email` (string): Email of the user who was referred and created this order
- `order_created_at` (datetime): When the order was created

## Usage Examples

### Using cURL

1. **Get your referral orders (requires authentication):**
```bash
curl -X GET "http://localhost:8000/referral/referral-orders" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

2. **Get all referral orders in the system:**
```bash
curl -X GET "http://localhost:8000/referral/admin/all-referral-orders"
```

3. **Get orders for a specific referral code:**
```bash
curl -X GET "http://localhost:8000/referral/referral-orders/ABC12345"
```

### Using Python requests

```python
import requests

# Get all referral orders
response = requests.get("http://localhost:8000/referral/admin/all-referral-orders")
data = response.json()

print(f"Total referral orders: {data['total_orders']}")
for order in data['orders']:
    print(f"Order {order['order_id']}: {order['account_size']} {order['account_type']} account")
    print(f"  Referred by: {order['referral_user_code']}")
    print(f"  User email: {order['referred_user_email']}")
```

## Business Logic

### How it works:
1. When a user registers with a referral code, their `referred_by` field is set to that referral code
2. When that user creates orders, the system can track them as "referral orders"
3. The endpoints query for users with `referred_by` field set and then find their orders

### Database Relationships:
- `User.referred_by` → `User.referral_code` (foreign key relationship)
- `OrderModel.email` → `User.email` (to link orders to users)

## Testing

Run the test script to verify the endpoints:

```bash
python test_referral_orders.py
```

## Error Handling

The endpoints handle the following error cases:
- Database connection errors
- Invalid referral codes
- Missing data
- Authentication failures (where applicable)

All errors return appropriate HTTP status codes and error messages.

## Security Considerations

- The `/referral/referral-orders` endpoint requires authentication to protect user privacy
- The admin endpoint is public but only returns order data, not sensitive user information
- Referral codes are validated before processing

## Future Enhancements

Potential improvements:
- Add pagination for large datasets
- Add filtering by date range
- Add sorting options
- Add analytics and reporting features
- Add export functionality (CSV, Excel) 