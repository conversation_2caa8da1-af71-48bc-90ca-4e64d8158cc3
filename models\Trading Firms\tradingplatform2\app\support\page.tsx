"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Mail, MessageSquare, Phone, FileText, HelpCircle, Clock } from "lucide-react"

export default function SupportPage() {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [subject, setSubject] = useState("")
  const [message, setMessage] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log({ name, email, subject, message })
    // Reset form
    setName("")
    setEmail("")
    setSubject("")
    setMessage("")
    // Show success message
    alert("Your message has been sent. We'll get back to you soon!")
  }

  const faqs = [
    {
      question: "How long does it take to get a response from support?",
      answer: "We typically respond to all support inquiries within 24 hours during business days.",
    },
    {
      question: "How do I reset my password?",
      answer:
        "You can reset your password by clicking on the 'Forgot Password' link on the login page. You will receive an email with instructions to reset your password.",
    },
    {
      question: "Can I change my challenge parameters after purchase?",
      answer:
        "No, challenge parameters cannot be changed after purchase. Please ensure you select the correct parameters before completing your purchase.",
    },
    {
      question: "How do I withdraw my profits?",
      answer:
        "You can request a withdrawal from your dashboard. Withdrawals are processed within 24 hours and sent to your registered payment method.",
    },
    {
      question: "What happens if I violate a trading rule?",
      answer:
        "Minor violations may result in warnings. Repeated or severe violations may result in account termination. Please review our trading rules carefully.",
    },
  ]

  return (
    <main className="flex-1 py-20 bg-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">Support Center</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            We're here to help you with any questions or issues you may have. Choose from the options below to get
            started.
          </p>
        </div>

        <Tabs defaultValue="contact" className="w-full">
          <div className="flex justify-center mb-10">
            <TabsList className="bg-gray-900">
              <TabsTrigger value="contact" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white">
                Contact Us
              </TabsTrigger>
              <TabsTrigger value="faq" className="data-[state=active]:bg-pink-500 data-[state=active]:text-white">
                FAQ
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="contact">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card className="bg-gray-900 border-gray-800">
                  <CardHeader>
                    <CardTitle className="text-white">Send Us a Message</CardTitle>
                    <CardDescription className="text-gray-400">
                      Fill out the form below and we'll get back to you as soon as possible.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-white">
                            Name
                          </Label>
                          <Input
                            id="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="Your name"
                            required
                            className="bg-gray-800 border-gray-700 text-white"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-white">
                            Email
                          </Label>
                          <Input
                            id="email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="Your email"
                            required
                            className="bg-gray-800 border-gray-700 text-white"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="subject" className="text-white">
                          Subject
                        </Label>
                        <Input
                          id="subject"
                          value={subject}
                          onChange={(e) => setSubject(e.target.value)}
                          placeholder="Subject of your message"
                          required
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="message" className="text-white">
                          Message
                        </Label>
                        <Textarea
                          id="message"
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          placeholder="Your message"
                          required
                          className="min-h-32 bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                      <Button type="submit" className="bg-pink-500 hover:bg-pink-600 text-white">
                        Send Message
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card className="bg-gray-900 border-gray-800 mb-6">
                  <CardHeader>
                    <CardTitle className="text-white">Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start">
                      <Mail className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">Email</h3>
                        <p className="text-gray-400"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Phone className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">Phone</h3>
                        <p className="text-gray-400">+****************</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Clock className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">Hours</h3>
                        <p className="text-gray-400">Monday - Friday: 9AM - 5PM ET</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-900 border-gray-800">
                  <CardHeader>
                    <CardTitle className="text-white">Other Support Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start">
                      <MessageSquare className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">Live Chat</h3>
                        <p className="text-gray-400">Available during business hours</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <FileText className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">Documentation</h3>
                        <p className="text-gray-400">Browse our knowledge base</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <HelpCircle className="h-5 w-5 text-pink-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-white font-medium">FAQ</h3>
                        <p className="text-gray-400">Find answers to common questions</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="faq">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Frequently Asked Questions</CardTitle>
                <CardDescription className="text-gray-400">
                  Find answers to the most common questions about our platform and services.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {faqs.map((faq, index) => (
                    <div key={index} className="border-b border-gray-800 pb-6 last:border-0 last:pb-0">
                      <h3 className="text-lg font-medium text-white mb-2">{faq.question}</h3>
                      <p className="text-gray-400">{faq.answer}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
}
