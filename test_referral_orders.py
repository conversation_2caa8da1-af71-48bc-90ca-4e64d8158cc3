#!/usr/bin/env python3
"""
Test script for referral orders endpoints
"""

import requests
import json
from datetime import datetime

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_referral_orders_endpoints():
    """Test the referral orders endpoints"""
    
    print("Testing Referral Orders Endpoints")
    print("=" * 50)
    
    # Test 1: Get referral orders for current user (requires authentication)
    print("\n1. Testing /referral/referral-orders (requires auth)")
    print("   This endpoint requires user authentication")
    print("   Use this endpoint to see orders from users you referred")
    
    # Test 2: Get all referral orders (admin endpoint)
    print("\n2. Testing /referral/admin/all-referral-orders")
    try:
        response = requests.get(f"{BASE_URL}/referral/admin/all-referral-orders")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {data['total_orders']} referral orders")
            if data['orders']:
                print("   Sample order data:")
                for order in data['orders'][:3]:  # Show first 3 orders
                    print(f"     - Order ID: {order['order_id']}")
                    print(f"       Account Size: {order['account_size']}")
                    print(f"       Account Type: {order['account_type']}")
                    print(f"       Referral Code: {order['referral_user_code']}")
                    print(f"       Referred User Email: {order['referred_user_email']}")
                    print(f"       Created At: {order['order_created_at']}")
                    print()
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test 3: Get referral orders by specific referral code
    print("\n3. Testing /referral/referral-orders/{referral_code}")
    print("   This endpoint allows you to check orders for any referral code")
    print("   Example: /referral/referral-orders/ABC12345")
    
    # Example with a sample referral code (you can replace with actual codes from your database)
    sample_referral_code = "ABC12345"  # Replace with actual referral code from your database
    try:
        response = requests.get(f"{BASE_URL}/referral/referral-orders/{sample_referral_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {data['total_orders']} orders for referral code {sample_referral_code}")
        elif response.status_code == 404:
            print(f"   ℹ️  No orders found for referral code {sample_referral_code}")
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Endpoint Summary:")
    print("1. GET /referral/referral-orders - Get orders from users you referred (requires auth)")
    print("2. GET /referral/admin/all-referral-orders - Get all referral orders in system")
    print("3. GET /referral/referral-orders/{referral_code} - Get orders for specific referral code")
    print("\nResponse format for each endpoint:")
    print(json.dumps({
        "total_orders": 2,
        "orders": [
            {
                "order_id": 123,
                "account_size": "50K",
                "account_type": "Challenge",
                "referral_user_code": "ABC12345",
                "referred_user_email": "<EMAIL>",
                "order_created_at": "2024-01-15T10:30:00"
            }
        ]
    }, indent=2))

if __name__ == "__main__":
    test_referral_orders_endpoints() 