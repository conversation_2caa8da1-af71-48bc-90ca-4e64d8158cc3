import os
import sys
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor
    cur = conn.cursor()
    
    # Tables and columns to make nullable
    tables_columns = [
        ("liveaccount", ["session_id", "terminal_id"]),
        ("stage2account", ["session_id", "terminal_id"])
    ]
    
    # Start a transaction
    conn.autocommit = False
    
    # Alter columns to allow NULL values
    for table, columns in tables_columns:
        for column in columns:
            print(f"Checking if {column} in {table} is already nullable...")
            
            # Check if column is already nullable
            cur.execute(f"""
                SELECT is_nullable
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}'
            """)
            
            result = cur.fetchone()
            if result and result[0] == 'NO':
                print(f"Altering {column} column in {table} to allow NULL values...")
                cur.execute(f"ALTER TABLE {table} ALTER COLUMN {column} DROP NOT NULL;")
                print(f"{column} column in {table} altered successfully")
            else:
                print(f"{column} column in {table} is already nullable or doesn't exist")
    
    # Commit the transaction
    conn.commit()
    print("Changes committed successfully")
    
    # Verify the changes
    print("\nVerifying changes...")
    for table, columns in tables_columns:
        for column in columns:
            cur.execute(f"""
                SELECT column_name, is_nullable
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}'
            """)
            
            result = cur.fetchone()
            if result:
                print(f"Column {result[0]} in {table} is now nullable: {result[1]}")
            else:
                print(f"Column {column} in {table} not found")
    
    # Close the connection
    cur.close()
    conn.close()
    print("\nDatabase schema update completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
    if 'conn' in locals() and conn:
        conn.rollback()
        conn.close()
