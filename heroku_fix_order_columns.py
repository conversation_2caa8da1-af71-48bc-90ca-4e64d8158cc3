#!/usr/bin/env python3
"""
Heroku Migration Script to Fix Order Table Columns
Run this script on Hero<PERSON> to add all missing columns in order-related tables.
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def run_order_columns_migration():
    """
    Migration script to add missing columns to all order-related tables
    """
    print("Starting Heroku migration to fix order table columns...")
    
    # Get DATABASE_URL from environment variables
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ DATABASE_URL environment variable is not set")
        return False
    
    # Ensure correct PostgreSQL format for Heroku
    if database_url.startswith("postgres://"):
        database_url = database_url.replace("postgres://", "postgresql://", 1)
    
    print(f"Connecting to database...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    
    try:
        # Define all the columns that need to be added
        migrations = [
            # completeordermodel table
            ("completeordermodel", "completed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("completeordermodel", "profit_target", "INTEGER"),
            ("completeordermodel", "session_id", "VARCHAR"),
            ("completeordermodel", "terminal_id", "INTEGER"),
            
            # liveaccount table
            ("liveaccount", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("liveaccount", "profit_share", "FLOAT DEFAULT 80.0"),
            ("liveaccount", "status", "VARCHAR DEFAULT 'active'"),
            ("liveaccount", "session_id", "VARCHAR"),
            ("liveaccount", "terminal_id", "INTEGER"),
            
            # stage2account table
            ("stage2account", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("stage2account", "profit_target", "FLOAT"),
            ("stage2account", "status", "VARCHAR DEFAULT 'active'"),
            ("stage2account", "session_id", "VARCHAR"),
            ("stage2account", "terminal_id", "INTEGER"),
            
            # ordermodel table
            ("ordermodel", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # orderimage table
            ("orderimage", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # passorder table
            ("passorder", "pass_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("passorder", "profit_amount", "FLOAT"),
            ("passorder", "notes", "VARCHAR"),
            
            # ordertimeline table
            ("ordertimeline", "event_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("ordertimeline", "notes", "VARCHAR"),
            
            # rejectorder table
            ("rejectorder", "rejected_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # failorder table
            ("failorder", "failed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # certificate table
            ("certificate", "issue_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("certificate", "profit_target", "FLOAT"),
        ]
        
        # Run all migrations
        for table_name, column_name, column_definition in migrations:
            try:
                # Check if column exists
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND column_name = %s;
                """, (table_name, column_name))
                
                if cursor.fetchone() is None:
                    # Add the column
                    sql = f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition};'
                    cursor.execute(sql)
                    print(f"✅ Added {column_name} column to {table_name}")
                else:
                    print(f"ℹ️  {column_name} column already exists in {table_name}")
                    
            except Exception as e:
                print(f"⚠️  Error adding {column_name} to {table_name}: {str(e)}")
                # Continue with other migrations
        
        print("\n🎉 Order columns migration completed successfully!")
        print("All order-related tables now have the required columns.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {str(e)}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    success = run_order_columns_migration()
    if not success:
        exit(1) 