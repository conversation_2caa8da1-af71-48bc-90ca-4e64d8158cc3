import os
from dotenv import load_dotenv
from sqlmodel import SQLModel, create_engine, Session
from datetime import datetime
import sys

# Add the current directory to the Python path
sys.path.append('.')

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")
if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

print(f"Database URL exists: {database_url is not None}")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Import the models
try:
    from models.order import OrderModel, CompleteOrderModel
    print("Successfully imported models")
except ImportError as e:
    print(f"Error importing models: {str(e)}")
    exit(1)

# Create the SQLAlchemy engine
engine = create_engine(database_url, echo=True)

try:
    # Create a test order
    with Session(engine) as session:
        # Create a new order
        order = OrderModel(
            username="test_user",
            email="<EMAIL>",
            challenge_type="Test Challenge",
            account_size="10K",
            platform="MT4",
            payment_method="Test Payment",
            txid="test_txid"
        )

        print("Adding order to session...")
        session.add(order)
        print("Committing session...")
        session.commit()

        # Refresh the instance to get the generated ID
        session.refresh(order)
        order_id = order.id
        print(f"Order created successfully with ID: {order_id}")

        # Now create a complete order record
        complete_order = CompleteOrderModel(
            order_id=order_id,
            server="Test Server",
            platform_login="test_login",
            platform_password="test_password",
            session_id=None,  # Explicitly set to None
            terminal_id=None,  # Explicitly set to None
            profit_target=None,
            completed_at=datetime.utcnow()
        )

        print(f"Created CompleteOrderModel instance for order_id: {order_id}")
        print("Adding complete order to session...")
        session.add(complete_order)
        print("Committing session...")
        session.commit()
        print("Session committed successfully")

        # Refresh the instance to get the generated ID
        session.refresh(complete_order)
        print(f"Complete order record inserted successfully with ID: {complete_order.id}")

    print("Test completed successfully!")

except Exception as e:
    print(f"Error: {str(e)}")
