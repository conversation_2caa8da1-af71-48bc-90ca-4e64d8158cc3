@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 220 50% 15%;
    --card: 0 0% 100%;
    --card-foreground: 220 50% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 50% 15%;
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 25% 40%;
    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 30% 90%;
    --input: 210 30% 90%;
    --ring: 210 100% 50%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 220 50% 10%;
    --foreground: 210 40% 98%;
    --card: 220 45% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 220 45% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 0 0% 100%;
    --muted: 220 40% 20%;
    --muted-foreground: 210 30% 80%;
    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 40% 20%;
    --input: 220 40% 20%;
    --ring: 210 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Ocean-themed animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(1deg);
  }
}

.float-animation {
  animation: float 10s ease-in-out infinite;
}

@keyframes wave {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(20px);
  }
}

.wave-animation {
  animation: wave 8s ease-in-out infinite;
}

@keyframes bubble {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-50px) scale(1.2);
    opacity: 0;
  }
}

.bubble-animation {
  animation: bubble 4s ease-in-out infinite;
}

/* Chart pattern for background */
@keyframes chartMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.chart-pattern {
  animation: chartMove 30s linear infinite;
  background-size: 200% 200%;
}

/* Underwater glow effects */
.underwater-glow {
  box-shadow: 0 0 30px 5px rgba(56, 189, 248, 0.2);
}

.text-glow {
  text-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
}

/* Additional underwater effects */
.opacity-15 {
  opacity: 0.15;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-45 {
  opacity: 0.45;
}

.bg-gradient-radial {
  background-image: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Enhanced text effects for underwater theme */
.underwater-text {
  text-shadow: 0 0 15px rgba(56, 189, 248, 0.3);
}

.glow-text {
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(56, 189, 248, 0.3);
  }
  100% {
    text-shadow: 0 0 15px rgba(56, 189, 248, 0.7), 0 0 30px rgba(56, 189, 248, 0.4);
  }
}

/* Improved chart pattern animation */
@keyframes chartFlow {
  0% {
    background-position: 0% bottom;
  }
  100% {
    background-position: 100% bottom;
  }
}

.chart-flow {
  animation: chartFlow 30s linear infinite;
}

/* Button hover effects */
.button-glow:hover {
  box-shadow: 0 0 20px rgba(56, 189, 248, 0.6);
}

/* Social media hover effects */
.social-hover:hover {
  color: theme("colors.sky.400");
  transform: translateY(-3px);
}

/* Ripple effect */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.ripple {
  animation: ripple 2s ease-out infinite;
}

/* Scroll indicator animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Enhanced button effects */
.button-glow {
  position: relative;
  overflow: hidden;
}

.button-glow::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(64, 224, 208, 0.4) 0%, rgba(64, 224, 208, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.button-glow:hover::after {
  opacity: 1;
}
