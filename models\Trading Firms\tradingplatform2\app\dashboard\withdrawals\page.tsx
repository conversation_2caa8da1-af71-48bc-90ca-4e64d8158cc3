"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, ArrowRight, CheckCircle, Clock, DollarSign, FileText, HelpCircle } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function WithdrawalsPage() {
  const [withdrawalAmount, setWithdrawalAmount] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const handleWithdrawalSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsDialogOpen(true)
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div className="md:col-span-2" variants={fadeInUp} initial="hidden" animate="visible">
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Withdraw Funds</CardTitle>
              <CardDescription className="text-gray-400">
                Request a withdrawal from your trading account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="withdraw" className="w-full">
                <TabsList className="grid grid-cols-2 mb-6 bg-[#001a2c]">
                  <TabsTrigger
                    value="withdraw"
                    className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                  >
                    Withdraw
                  </TabsTrigger>
                  <TabsTrigger
                    value="history"
                    className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                  >
                    History
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="withdraw">
                  <form onSubmit={handleWithdrawalSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="amount" className="text-white">
                        Withdrawal Amount
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="amount"
                          type="number"
                          placeholder="0.00"
                          value={withdrawalAmount}
                          onChange={(e) => setWithdrawalAmount(e.target.value)}
                          className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                          required
                        />
                      </div>
                      <div className="flex justify-between text-xs mt-1">
                        <span className="text-gray-400">Available: $9,960.00</span>
                        <button
                          type="button"
                          className="text-teal-400 hover:underline"
                          onClick={() => setWithdrawalAmount("9960")}
                        >
                          Max
                        </button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="payment-method" className="text-white">
                        Payment Method
                      </Label>
                      <Select value={paymentMethod} onValueChange={setPaymentMethod} required>
                        <SelectTrigger id="payment-method" className="bg-[#001a2c] border-[#003a4c] text-white">
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                          <SelectItem value="bank">Bank Transfer</SelectItem>
                          <SelectItem value="paypal">PayPal</SelectItem>
                          <SelectItem value="crypto">Cryptocurrency</SelectItem>
                          <SelectItem value="skrill">Skrill</SelectItem>
                          <SelectItem value="neteller">Neteller</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {paymentMethod === "bank" && (
                      <div className="space-y-4 border border-[#003a4c] rounded-lg p-4">
                        <div className="space-y-2">
                          <Label htmlFor="bank-name" className="text-white">
                            Bank Name
                          </Label>
                          <Input
                            id="bank-name"
                            placeholder="Enter bank name"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="account-number" className="text-white">
                            Account Number
                          </Label>
                          <Input
                            id="account-number"
                            placeholder="Enter account number"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="swift-code" className="text-white">
                            SWIFT/BIC Code
                          </Label>
                          <Input
                            id="swift-code"
                            placeholder="Enter SWIFT/BIC code"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                      </div>
                    )}

                    {paymentMethod === "paypal" && (
                      <div className="space-y-2 border border-[#003a4c] rounded-lg p-4">
                        <Label htmlFor="paypal-email" className="text-white">
                          PayPal Email
                        </Label>
                        <Input
                          id="paypal-email"
                          type="email"
                          placeholder="Enter PayPal email"
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      </div>
                    )}

                    {paymentMethod === "crypto" && (
                      <div className="space-y-4 border border-[#003a4c] rounded-lg p-4">
                        <div className="space-y-2">
                          <Label htmlFor="crypto-type" className="text-white">
                            Cryptocurrency
                          </Label>
                          <Select>
                            <SelectTrigger id="crypto-type" className="bg-[#001a2c] border-[#003a4c] text-white">
                              <SelectValue placeholder="Select cryptocurrency" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                              <SelectItem value="btc">Bitcoin (BTC)</SelectItem>
                              <SelectItem value="eth">Ethereum (ETH)</SelectItem>
                              <SelectItem value="usdt">Tether (USDT)</SelectItem>
                              <SelectItem value="usdc">USD Coin (USDC)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="wallet-address" className="text-white">
                            Wallet Address
                          </Label>
                          <Input
                            id="wallet-address"
                            placeholder="Enter wallet address"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                      </div>
                    )}

                    <div className="pt-4 border-t border-[#003a4c]">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Amount</span>
                        <span className="text-white">${withdrawalAmount || "0.00"}</span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Fee</span>
                        <span className="text-white">$0.00</span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span className="text-gray-400">Total to Receive</span>
                        <span className="text-white">${withdrawalAmount || "0.00"}</span>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-teal-500 hover:bg-teal-600 text-white"
                      disabled={!withdrawalAmount || !paymentMethod}
                    >
                      Request Withdrawal
                    </Button>
                  </form>
                </TabsContent>

                <TabsContent value="history">
                  <div className="rounded-md border border-[#003a4c] overflow-hidden">
                    <div className="relative w-full overflow-auto">
                      <table className="w-full caption-bottom text-sm">
                        <thead className="border-b border-[#003a4c]">
                          <tr className="border-b border-[#003a4c]">
                            <th className="h-12 px-4 text-left font-medium text-gray-400">Date</th>
                            <th className="h-12 px-4 text-left font-medium text-gray-400">Amount</th>
                            <th className="h-12 px-4 text-left font-medium text-gray-400">Method</th>
                            <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {[
                            {
                              date: "May 1, 2025",
                              amount: "$1,250.00",
                              method: "Bank Transfer",
                              status: "Completed",
                            },
                            {
                              date: "Apr 15, 2025",
                              amount: "$3,450.00",
                              method: "PayPal",
                              status: "Completed",
                            },
                            {
                              date: "Apr 1, 2025",
                              amount: "$2,780.00",
                              method: "Cryptocurrency",
                              status: "Completed",
                            },
                            {
                              date: "Mar 15, 2025",
                              amount: "$1,890.00",
                              method: "Bank Transfer",
                              status: "Completed",
                            },
                          ].map((withdrawal, i) => (
                            <tr key={i} className="border-b border-[#003a4c]">
                              <td className="p-4 text-white">{withdrawal.date}</td>
                              <td className="p-4 text-white">{withdrawal.amount}</td>
                              <td className="p-4 text-gray-300">{withdrawal.method}</td>
                              <td className="p-4">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                                  {withdrawal.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Withdrawal Info</CardTitle>
              <CardDescription className="text-gray-400">Important information about withdrawals</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Processing Time</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Withdrawals are processed within 24 hours on business days. Bank transfers may take 2-5 business days
                  to reflect in your account.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Withdrawal Limits</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Minimum withdrawal: $100
                  <br />
                  Maximum withdrawal: Your available balance
                  <br />
                  Monthly limit: $50,000
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Requirements</h3>
                </div>
                <p className="text-sm text-gray-400">
                  You must complete KYC verification before making your first withdrawal. Withdrawals are only processed
                  to accounts in your name.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <HelpCircle className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Need Help?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  If you have any questions about withdrawals, please contact our support team at{" "}
                  <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </CardContent>
            <CardFooter className="border-t border-[#003a4c] pt-6">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                View Withdrawal Policy
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Withdrawal Status</CardTitle>
            <CardDescription className="text-gray-400">Track the status of your withdrawal requests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[#003a4c]"></div>
                <div className="space-y-8">
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-teal-500 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    <h3 className="text-white font-medium">Request Submitted</h3>
                    <p className="text-sm text-gray-400 mt-1">
                      Your withdrawal request has been submitted and is awaiting processing.
                    </p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <Clock className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Processing</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Our finance team is processing your withdrawal request.
                    </p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <ArrowRight className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Funds Sent</h3>
                    <p className="text-sm text-gray-500 mt-1">Funds have been sent to your specified payment method.</p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Completed</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      The withdrawal has been completed and funds should be available in your account.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Confirmation Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-[#002a3c] border-[#003a4c] text-white">
          <DialogHeader>
            <DialogTitle>Confirm Withdrawal</DialogTitle>
            <DialogDescription className="text-gray-400">
              Please review your withdrawal request details
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex justify-between">
              <span className="text-gray-400">Amount:</span>
              <span className="font-medium">${withdrawalAmount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Payment Method:</span>
              <span className="font-medium">
                {paymentMethod === "bank"
                  ? "Bank Transfer"
                  : paymentMethod === "paypal"
                    ? "PayPal"
                    : paymentMethod === "crypto"
                      ? "Cryptocurrency"
                      : paymentMethod === "skrill"
                        ? "Skrill"
                        : paymentMethod === "neteller"
                          ? "Neteller"
                          : ""}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Fee:</span>
              <span className="font-medium">$0.00</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total to Receive:</span>
              <span className="font-medium">${withdrawalAmount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Estimated Arrival:</span>
              <span className="font-medium">1-3 Business Days</span>
            </div>
            <div className="flex items-start mt-4 p-3 bg-yellow-500/10 rounded-md">
              <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 shrink-0 mt-0.5" />
              <p className="text-sm text-yellow-400">
                By confirming this withdrawal, you agree to our withdrawal terms and conditions. This action cannot be
                undone.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              className="border-[#003a4c] text-white hover:bg-[#003a4c]"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-teal-500 hover:bg-teal-600 text-white"
              onClick={() => {
                // Handle withdrawal submission
                setIsDialogOpen(false)
                // Show success message or redirect
              }}
            >
              Confirm Withdrawal
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
