import requests
import json

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_create_account_credentials():
    """Test creating multiple account credentials"""
    
    # Test data
    test_data = {
        "credentials": [
            {
                "platform": "mt4",
                "server": "Demo Server",
                "platform_login": "12345",
                "platform_password": "password123",
                "account_size": "10000",
                "account_type": "phase1"
            },
            {
                "platform": "mt5",
                "server": "Live Server",
                "platform_login": "67890",
                "platform_password": "password456",
                "account_size": "25000",
                "account_type": "hft"
            }
        ]
    }
    
    # Make the request
    response = requests.post(f"{BASE_URL}/account/credentials", json=test_data)
    
    # Print response for debugging
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Assert response
    assert response.status_code == 200
    
    # Parse response
    credentials = response.json()
    
    # Assert response data
    assert len(credentials) == 2
    assert credentials[0]["platform_login"] == "12345"
    assert credentials[1]["platform_login"] == "67890"
    
    # Return the created credentials for use in other tests
    return credentials

def test_get_all_credentials(credentials=None):
    """Test getting all account credentials"""
    
    # Make the request
    response = requests.get(f"{BASE_URL}/account/credentials")
    
    # Print response for debugging
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Assert response
    assert response.status_code == 200
    
    # Parse response
    all_credentials = response.json()
    
    # Assert response data
    if credentials:
        assert len(all_credentials) >= len(credentials)
    
    return all_credentials

def test_get_pending_credentials():
    """Test getting pending account credentials"""
    
    # Make the request
    response = requests.get(f"{BASE_URL}/account/credentials/pending")
    
    # Print response for debugging
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Assert response
    assert response.status_code == 200
    
    # Parse response
    pending_credentials = response.json()
    
    # Assert all returned credentials are not assigned
    for credential in pending_credentials:
        assert credential["is_assigned"] == False
    
    return pending_credentials

def test_get_credentials_by_type():
    """Test getting account credentials by type"""
    
    # Test for HFT accounts
    response = requests.get(f"{BASE_URL}/account/credentials/hft")
    
    # Print response for debugging
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Assert response
    assert response.status_code == 200
    
    # Parse response
    hft_credentials = response.json()
    
    # Assert all returned credentials are of type HFT
    for credential in hft_credentials:
        assert credential["account_type"] == "hft"
    
    return hft_credentials

def test_update_credential(credential_id):
    """Test updating an account credential"""
    
    # Test data
    update_data = {
        "platform": "mt5",
        "account_size": "50000"
    }
    
    # Make the request
    response = requests.put(f"{BASE_URL}/account/credentials/{credential_id}", json=update_data)
    
    # Print response for debugging
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Assert response
    assert response.status_code == 200
    
    # Parse response
    updated_credential = response.json()
    
    # Assert response data
    assert updated_credential["id"] == credential_id
    assert updated_credential["platform"] == "mt5"
    assert updated_credential["account_size"] == "50000"
    
    return updated_credential

def run_tests():
    """Run all tests in sequence"""
    
    print("=== Testing Account Endpoints ===")
    
    # Create credentials
    print("\n1. Testing create_account_credentials:")
    credentials = test_create_account_credentials()
    
    # Get all credentials
    print("\n2. Testing get_all_credentials:")
    all_credentials = test_get_all_credentials(credentials)
    
    # Get pending credentials
    print("\n3. Testing get_pending_credentials:")
    pending_credentials = test_get_pending_credentials()
    
    # Get credentials by type
    print("\n4. Testing get_credentials_by_type:")
    hft_credentials = test_get_credentials_by_type()
    
    # Update a credential
    if credentials:
        print("\n5. Testing update_credential:")
        credential_id = credentials[0]["id"]
        updated_credential = test_update_credential(credential_id)
    
    print("\n=== All tests completed ===")

if __name__ == "__main__":
    run_tests()
