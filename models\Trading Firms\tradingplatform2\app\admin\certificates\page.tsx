"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Filter,
  MoreHorizontal,
  Award,
  Download,
  Eye,
  Send,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  FileText,
  Mail,
} from "lucide-react"

export default function CertificatesManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")

  const certificates = [
    {
      id: "CERT-001",
      userId: "USR-123",
      userName: "John Doe",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Phase 1 Completion",
      challengeType: "Standard Challenge",
      amount: "$100,000",
      status: "issued",
      issueDate: "2024-05-20",
      downloadCount: 3,
      profit: "+$8,450",
      tradingDays: 15,
    },
    {
      id: "CERT-002",
      userId: "USR-456",
      userName: "Jane Smith",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Phase 2 Completion",
      challengeType: "Express Challenge",
      amount: "$50,000",
      status: "ready",
      issueDate: null,
      downloadCount: 0,
      profit: "+$4,250",
      tradingDays: 8,
    },
    {
      id: "CERT-003",
      userId: "USR-789",
      userName: "Mike Johnson",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Live Account",
      challengeType: "HFT Challenge",
      amount: "$200,000",
      status: "issued",
      issueDate: "2024-04-15",
      downloadCount: 1,
      profit: "+$23,890",
      tradingDays: 45,
    },
    {
      id: "CERT-004",
      userId: "USR-321",
      userName: "Sarah Wilson",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Phase 1 Completion",
      challengeType: "Standard Challenge",
      amount: "$100,000",
      status: "pending",
      issueDate: null,
      downloadCount: 0,
      profit: "+$12,670",
      tradingDays: 12,
    },
    {
      id: "CERT-005",
      userId: "USR-654",
      userName: "David Brown",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Profit Split",
      challengeType: "Live Account",
      amount: "$100,000",
      status: "issued",
      issueDate: "2024-03-10",
      downloadCount: 5,
      profit: "+$45,230",
      tradingDays: 90,
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "issued":
        return (
          <Badge className="bg-sky-500/10 text-sky-500 border-sky-500/20">
            <CheckCircle className="mr-1 h-3 w-3" />
            Issued
          </Badge>
        )
      case "ready":
        return (
          <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            <Clock className="mr-1 h-3 w-3" />
            Ready
          </Badge>
        )
      case "pending":
        return (
          <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        )
      case "revoked":
        return (
          <Badge className="bg-red-500/10 text-red-500 border-red-500/20">
            <XCircle className="mr-1 h-3 w-3" />
            Revoked
          </Badge>
        )
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "Phase 1 Completion":
        return (
          <Badge variant="secondary" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            Phase 1
          </Badge>
        )
      case "Phase 2 Completion":
        return (
          <Badge variant="secondary" className="bg-purple-500/10 text-purple-500 border-purple-500/20">
            Phase 2
          </Badge>
        )
      case "Live Account":
        return (
          <Badge variant="secondary" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            Live Account
          </Badge>
        )
      case "Profit Split":
        return (
          <Badge variant="secondary" className="bg-sky-500/10 text-sky-500 border-sky-500/20">
            Profit Split
          </Badge>
        )
      default:
        return (
          <Badge variant="secondary" className="bg-gray-500/10 text-gray-500 border-gray-500/20">
            Other
          </Badge>
        )
    }
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">Certificates Management</h1>
            <p className="text-gray-400">Manage and issue trading certificates</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
              <Download className="mr-2 h-4 w-4" />
              Export All
            </Button>
            <Button className="bg-sky-500 hover:bg-sky-600 text-white">
              <Award className="mr-2 h-4 w-4" />
              Issue Certificate
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Stats Overview */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: "Total Issued", count: 1247, icon: Award, color: "text-sky-500" },
            { label: "Ready to Issue", count: 23, icon: Clock, color: "text-blue-500" },
            { label: "Pending Review", count: 8, icon: FileText, color: "text-yellow-500" },
            { label: "This Month", count: 89, icon: Calendar, color: "text-blue-500" },
          ].map((stat, index) => (
            <Card key={index} className="bg-[#002a3c] border-[#003a4c]">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                  <Badge variant="secondary" className="bg-[#001a2c] text-white">
                    {stat.count}
                  </Badge>
                </div>
                <h3 className="text-sm font-medium text-white mt-2">{stat.label}</h3>
              </CardContent>
            </Card>
          ))}
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search certificates by ID, user, or type..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="issued">Issued</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="revoked">Revoked</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="phase1">Phase 1 Completion</SelectItem>
                  <SelectItem value="phase2">Phase 2 Completion</SelectItem>
                  <SelectItem value="live">Live Account</SelectItem>
                  <SelectItem value="profit">Profit Split</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                <Filter className="mr-2 h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Certificates Table */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">All Certificates ({certificates.length})</CardTitle>
            <CardDescription className="text-gray-400">Complete list of trading certificates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-[#003a4c]">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b border-[#003a4c]">
                    <tr>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Certificate ID</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">User</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Type</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Challenge</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Amount</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Profit</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Issue Date</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Downloads</th>
                      <th className="h-12 px-4 text-right font-medium text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {certificates.map((cert) => (
                      <tr key={cert.id} className="border-b border-[#003a4c] hover:bg-[#001a2c]">
                        <td className="p-4">
                          <div className="font-medium text-white">{cert.id}</div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={cert.userAvatar || "/placeholder.svg"} alt={cert.userName} />
                              <AvatarFallback className="bg-[#003a4c] text-white text-xs">
                                {cert.userName
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-white text-sm">{cert.userName}</div>
                              <div className="text-xs text-gray-400">{cert.userId}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">{getTypeBadge(cert.type)}</td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{cert.challengeType}</div>
                        </td>
                        <td className="p-4">
                          <div className="font-medium text-white">{cert.amount}</div>
                        </td>
                        <td className="p-4">{getStatusBadge(cert.status)}</td>
                        <td className="p-4">
                          <div className="font-medium text-sky-500">{cert.profit}</div>
                          <div className="text-xs text-gray-400">{cert.tradingDays} days</div>
                        </td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{cert.issueDate || "Not issued"}</div>
                        </td>
                        <td className="p-4">
                          <Badge variant="secondary" className="bg-[#001a2c] text-white">
                            {cert.downloadCount}
                          </Badge>
                        </td>
                        <td className="p-4 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-[#002a3c] border-[#003a4c] text-white">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Eye className="mr-2 h-4 w-4" />
                                View Certificate
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Download className="mr-2 h-4 w-4" />
                                Download PDF
                              </DropdownMenuItem>
                              {cert.status === "ready" && (
                                <DropdownMenuItem className="hover:bg-[#003a4c] text-sky-400">
                                  <Award className="mr-2 h-4 w-4" />
                                  Issue Certificate
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Send className="mr-2 h-4 w-4" />
                                Send to User
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Mail className="mr-2 h-4 w-4" />
                                Email Certificate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c] text-red-400">
                                <XCircle className="mr-2 h-4 w-4" />
                                Revoke Certificate
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
