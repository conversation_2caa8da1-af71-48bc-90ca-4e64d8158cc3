"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  CreditCard,
  Download,
  FileText,
  Plus,
  Trash,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign,
} from "lucide-react"

export default function BillingPage() {
  const [promoCode, setPromoCode] = useState("")

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div className="md:col-span-2" variants={fadeInUp} initial="hidden" animate="visible">
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Payment Methods</CardTitle>
              <CardDescription className="text-gray-400">
                Manage your payment methods and billing information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]">
                  <div className="flex items-center">
                    <div className="h-10 w-16 bg-gradient-to-r from-blue-600 to-blue-400 rounded-md flex items-center justify-center text-white font-bold mr-4">
                      VISA
                    </div>
                    <div>
                      <p className="text-white font-medium">Visa ending in 4242</p>
                      <p className="text-sm text-gray-400">Expires 12/2025</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Badge className="mr-2 bg-teal-500 text-white">Default</Badge>
                    <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]">
                  <div className="flex items-center">
                    <div className="h-10 w-16 bg-gradient-to-r from-red-600 to-orange-400 rounded-md flex items-center justify-center text-white font-bold mr-4">
                      MC
                    </div>
                    <div>
                      <p className="text-white font-medium">Mastercard ending in 5678</p>
                      <p className="text-sm text-gray-400">Expires 08/2024</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <Button className="bg-teal-500 hover:bg-teal-600 text-white">
                <Plus className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Billing Summary</CardTitle>
              <CardDescription className="text-gray-400">Your current billing information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-400">Plan</span>
                <span className="font-medium text-white">Standard $100K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Billing Cycle</span>
                <span className="font-medium text-white">One-time payment</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Next Payment</span>
                <span className="font-medium text-white">N/A</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Payment Method</span>
                <span className="font-medium text-white">Visa ending in 4242</span>
              </div>

              <div className="pt-4 border-t border-[#003a4c]">
                <div className="flex items-center space-x-2 mb-4">
                  <Label htmlFor="promo-code" className="text-white">
                    Promo Code
                  </Label>
                  <div className="flex-1 flex space-x-2">
                    <Input
                      id="promo-code"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      placeholder="Enter code"
                      className="bg-[#001a2c] border-[#003a4c] text-white"
                    />
                    <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                      Apply
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 border-t border-[#003a4c] pt-6">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                <CreditCard className="mr-2 h-4 w-4" />
                Update Billing Info
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Billing History</CardTitle>
            <CardDescription className="text-gray-400">View and download your past invoices</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="invoices" className="w-full">
              <TabsList className="bg-[#001a2c]">
                <TabsTrigger
                  value="invoices"
                  className="data-[state=active]:bg-sky-500 data-[state=active]:text-white"
                >
                  Invoices
                </TabsTrigger>
                <TabsTrigger
                  value="subscriptions"
                  className="data-[state=active]:bg-sky-500 data-[state=active]:text-white"
                >
                  Subscriptions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="invoices" className="mt-6">
                <div className="rounded-md border border-[#003a4c] overflow-hidden">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead className="border-b border-[#003a4c]">
                        <tr className="border-b border-[#003a4c]">
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Invoice</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Date</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Amount</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                          <th className="h-12 px-4 text-right font-medium text-gray-400">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {[
                          {
                            id: "INV-001",
                            date: "May 1, 2025",
                            amount: "$499.00",
                            status: "Paid",
                          },
                          {
                            id: "INV-002",
                            date: "Apr 15, 2025",
                            amount: "$249.00",
                            status: "Paid",
                          },
                          {
                            id: "INV-003",
                            date: "Mar 10, 2025",
                            amount: "$99.00",
                            status: "Paid",
                          },
                        ].map((invoice, i) => (
                          <tr key={i} className="border-b border-[#003a4c]">
                            <td className="p-4 font-medium text-white">{invoice.id}</td>
                            <td className="p-4 text-gray-300">{invoice.date}</td>
                            <td className="p-4 text-gray-300">{invoice.amount}</td>
                            <td className="p-4">
                              <Badge className="bg-green-500/20 text-green-400">{invoice.status}</Badge>
                            </td>
                            <td className="p-4 text-right">
                              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                                <Download className="h-4 w-4 mr-2" />
                                PDF
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="subscriptions" className="mt-6">
                <div className="rounded-md border border-[#003a4c] overflow-hidden">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead className="border-b border-[#003a4c]">
                        <tr className="border-b border-[#003a4c]">
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Subscription</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Start Date</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">End Date</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                          <th className="h-12 px-4 text-right font-medium text-gray-400">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b border-[#003a4c]">
                          <td className="p-4 font-medium text-white">Standard $100K Challenge</td>
                          <td className="p-4 text-gray-300">May 1, 2025</td>
                          <td className="p-4 text-gray-300">N/A</td>
                          <td className="p-4">
                            <Badge className="bg-teal-500/20 text-sky-400">Active</Badge>
                          </td>
                          <td className="p-4 text-right">
                            <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                              <FileText className="h-4 w-4 mr-2" />
                              Details
                            </Button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Billing FAQ</CardTitle>
            <CardDescription className="text-gray-400">
              Frequently asked questions about billing and payments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">When will I be charged?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  For challenges, you'll be charged once at the time of purchase. There are no recurring charges unless
                  you opt for a subscription plan.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">What payment methods do you accept?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  We accept all major credit cards, PayPal, and select cryptocurrencies. Bank transfers are available
                  for orders over $1,000.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">Can I get a refund?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Refunds are available within 24 hours of purchase if you haven't started trading. Please contact our
                  support team for assistance.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">How long do challenges last?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Our challenges have no time limit. You can take as long as you need to reach the profit target while
                  respecting the trading rules.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">Are there any hidden fees?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  No, there are no hidden fees. The price you see is the price you pay. We're transparent about all
                  costs associated with our services.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-sky-500 mr-2" />
                  <h3 className="text-white font-medium">How do I update my billing information?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  You can update your billing information in the Payment Methods section of your account settings at any
                  time.
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              For any other billing questions, please contact our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-sky-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}
