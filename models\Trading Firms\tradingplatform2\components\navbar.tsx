"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Menu, X, Globe } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        isScrolled ? "bg-[#001a2c]/90 backdrop-blur-md shadow-md" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-bold text-white underwater-text">
                FUNDED<span className="text-sky-400">WHALES</span> {/* Changed from teal to sky */}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/challenge" className="text-sm text-gray-300 hover:text-white transition-colors">
              Challenge
            </Link>
            <Link href="/rules" className="text-sm text-gray-300 hover:text-white transition-colors">
              Rules
            </Link>
            <Link href="/support" className="text-sm text-gray-300 hover:text-white transition-colors">
              Support
            </Link>
            <Link href="/old-dashboard" className="text-sm text-gray-300 hover:text-white transition-colors">
              Old Dashboard
            </Link>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
                  <Globe className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>English</DropdownMenuItem>
                <DropdownMenuItem>Spanish</DropdownMenuItem>
                <DropdownMenuItem>French</DropdownMenuItem>
                <DropdownMenuItem>German</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button className="bg-sky-500 hover:bg-sky-600 text-white">Dashboard</Button>{" "}
            {/* Changed from teal to sky */}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
              {isMobileMenuOpen ? <X className="h-6 w-6 text-white" /> : <Menu className="h-6 w-6 text-white" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-[#001a2c] border-t border-gray-800">
          <div className="container mx-auto px-4 py-4 space-y-4">
            <Link
              href="/challenge"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Challenge
            </Link>
            <Link
              href="/rules"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Rules
            </Link>
            <Link
              href="/support"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Support
            </Link>
            <Link
              href="/old-dashboard"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Old Dashboard
            </Link>
            <div className="pt-4 border-t border-gray-800 flex items-center justify-between">
              <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
                <Globe className="h-5 w-5" />
              </Button>
              <Button className="bg-sky-500 hover:bg-sky-600 text-white">Dashboard</Button>{" "}
              {/* Changed from teal to sky */}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
