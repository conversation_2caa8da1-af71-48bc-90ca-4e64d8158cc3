-- Heroku Migration Script to Add Referral Columns
-- Run this SQL script on your Heroku PostgreSQL database

-- Add referral_code column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='user' AND column_name='referral_code'
    ) THEN
        ALTER TABLE "user" ADD COLUMN referral_code VARCHAR;
        ALTER TABLE "user" ADD CONSTRAINT user_referral_code_unique UNIQUE (referral_code);
        RAISE NOTICE 'Added referral_code column';
    ELSE
        RAISE NOTICE 'referral_code column already exists';
    END IF;
END $$;

-- Add referred_by column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='user' AND column_name='referred_by'
    ) THEN
        ALTER TABLE "user" ADD COLUMN referred_by VARCHAR;
        RAISE NOTICE 'Added referred_by column';
    ELSE
        RAISE NOTICE 'referred_by column already exists';
    END IF;
END $$;

-- Add total_points column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='user' AND column_name='total_points'
    ) THEN
        ALTER TABLE "user" ADD COLUMN total_points FLOAT DEFAULT 0;
        RAISE NOTICE 'Added total_points column';
    ELSE
        RAISE NOTICE 'total_points column already exists';
    END IF;
END $$;

-- Update existing users with referral codes (if they don't have them)
-- This will generate random 8-character codes for users without referral codes
UPDATE "user" 
SET referral_code = (
    SELECT 'REF' || LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0')
    WHERE NOT EXISTS (
        SELECT 1 FROM "user" u2 
        WHERE u2.referral_code = 'REF' || LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0')
    )
)
WHERE referral_code IS NULL;

-- Verify the migration
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user' 
AND column_name IN ('referral_code', 'referred_by', 'total_points')
ORDER BY column_name; 