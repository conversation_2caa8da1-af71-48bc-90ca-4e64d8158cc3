"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  BarChart3,
  CreditCard,
  DollarSign,
  FileCheck,
  Gift,
  HelpCircle,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Settings,
  ShieldCheck,
  Users,
  X,
} from "lucide-react"

interface SidebarProps {
  className?: string
}

export default function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "Buy Account",
      icon: CreditCard,
      href: "/dashboard/buy-account",
      active: pathname === "/dashboard/buy-account",
    },
    {
      label: "Trading Accounts",
      icon: BarChart3,
      href: "/dashboard/accounts",
      active: pathname === "/dashboard/accounts",
    },
    {
      label: "KYC Verification",
      icon: ShieldCheck,
      href: "/dashboard/kyc",
      active: pathname === "/dashboard/kyc",
    },
    {
      label: "Withdrawals",
      icon: DollarSign,
      href: "/dashboard/withdrawals",
      active: pathname === "/dashboard/withdrawals",
    },
    // Temporarily hidden pages - can be restored later
    // {
    //   label: "Referrals",
    //   icon: Users,
    //   href: "/dashboard/referrals",
    //   active: pathname === "/dashboard/referrals",
    // },
    // {
    //   label: "Billing",
    //   icon: CreditCard,
    //   href: "/dashboard/billing",
    //   active: pathname === "/dashboard/billing",
    // },
    // {
    //   label: "Certificates",
    //   icon: FileCheck,
    //   href: "/dashboard/certificates",
    //   active: pathname === "/dashboard/certificates",
    // },
    // {
    //   label: "Rewards",
    //   icon: Gift,
    //   href: "/dashboard/rewards",
    //   active: pathname === "/dashboard/rewards",
    // },
    // {
    //   label: "Support",
    //   icon: MessageSquare,
    //   href: "/dashboard/support",
    //   active: pathname === "/dashboard/support",
    // },
    // {
    //   label: "Help Center",
    //   icon: HelpCircle,
    //   href: "/dashboard/help",
    //   active: pathname === "/dashboard/help",
    // },
    // {
    //   label: "Settings",
    //   icon: Settings,
    //   href: "/dashboard/settings",
    //   active: pathname === "/dashboard/settings",
    // },
  ]

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && <div className="fixed inset-0 z-40 bg-black/80 lg:hidden" onClick={toggleMobileSidebar} />}

      {/* Mobile Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 lg:hidden text-white"
        onClick={toggleMobileSidebar}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 flex flex-col bg-[#001a2c] border-r border-[#003a4c] transition-all duration-300 ease-in-out",
          isCollapsed ? "w-[70px]" : "w-[250px]",
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className,
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-[#003a4c]">
          <Link href="/dashboard" className="flex items-center">
            {!isCollapsed && (
              <span className="text-xl font-bold text-white">
                FUNDED<span className="text-sky-400">WHALES</span>
              </span>
            )}
            {isCollapsed && <span className="text-xl font-bold text-sky-400">FW</span>}
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="hidden lg:flex text-gray-400 hover:text-white"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto py-4 px-3">
          <nav className="space-y-1">
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  route.active ? "bg-[#003a4c] text-white" : "text-gray-400 hover:text-white hover:bg-[#002a3c]",
                )}
              >
                <route.icon className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
                {!isCollapsed && <span>{route.label}</span>}
              </Link>
            ))}
          </nav>
        </div>

        <div className="p-4 border-t border-[#003a4c]">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-gray-400 hover:text-white hover:bg-[#002a3c]",
              isCollapsed && "justify-center",
            )}
          >
            <LogOut className={cn("h-5 w-5", isCollapsed ? "mr-0" : "mr-3")} />
            {!isCollapsed && <span>Logout</span>}
          </Button>
        </div>
      </div>
    </>
  )
}
