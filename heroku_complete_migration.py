#!/usr/bin/env python3
"""
Complete Heroku Migration Script
Fixes both referral columns and order columns issues.
"""

import os
import psycopg2
import uuid
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_referral_code():
    return str(uuid.uuid4())[:8].upper()

def run_complete_migration():
    """
    Complete migration script to fix all database schema issues
    """
    print("🚀 Starting Complete Heroku Migration...")
    print("=" * 50)
    
    # Get DATABASE_URL from environment variables
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ DATABASE_URL environment variable is not set")
        return False
    
    # Ensure correct PostgreSQL format for Heroku
    if database_url.startswith("postgres://"):
        database_url = database_url.replace("postgres://", "postgresql://", 1)
    
    print("📡 Connecting to Heroku database...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    conn.autocommit = True
    cursor = conn.cursor()
    
    try:
        print("\n🔧 STEP 1: Fixing User Table (Referral Columns)")
        print("-" * 40)
        
        # Fix user table referral columns
        user_migrations = [
            ("user", "referral_code", "VARCHAR"),
            ("user", "referred_by", "VARCHAR"),
            ("user", "total_points", "FLOAT DEFAULT 0"),
        ]
        
        for table_name, column_name, column_definition in user_migrations:
            try:
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND column_name = %s;
                """, (table_name, column_name))
                
                if cursor.fetchone() is None:
                    sql = f'ALTER TABLE "{table_name}" ADD COLUMN {column_name} {column_definition};'
                    cursor.execute(sql)
                    
                    # Add unique constraint for referral_code
                    if column_name == "referral_code":
                        cursor.execute('ALTER TABLE "user" ADD CONSTRAINT user_referral_code_unique UNIQUE (referral_code);')
                    
                    print(f"✅ Added {column_name} column to {table_name}")
                else:
                    print(f"ℹ️  {column_name} column already exists in {table_name}")
                    
            except Exception as e:
                print(f"⚠️  Error adding {column_name} to {table_name}: {str(e)}")
        
        # Update existing users with referral codes
        cursor.execute('SELECT id FROM "user" WHERE referral_code IS NULL;')
        users_without_codes = cursor.fetchall()
        if users_without_codes:
            print(f"🔄 Updating {len(users_without_codes)} users with referral codes...")
            for user_row in users_without_codes:
                user_id = user_row[0]
                new_code = generate_referral_code()
                while True:
                    cursor.execute('SELECT id FROM "user" WHERE referral_code = %s;', (new_code,))
                    if cursor.fetchone() is None:
                        break
                    new_code = generate_referral_code()
                cursor.execute('UPDATE "user" SET referral_code = %s WHERE id = %s;', (new_code, user_id))
            print(f"✅ Updated {len(users_without_codes)} users with referral codes")
        else:
            print("ℹ️  All users already have referral codes")
        
        print("\n🔧 STEP 2: Fixing Order Tables")
        print("-" * 40)
        
        # Fix order-related tables
        order_migrations = [
            # completeordermodel table
            ("completeordermodel", "completed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("completeordermodel", "profit_target", "INTEGER"),
            ("completeordermodel", "session_id", "VARCHAR"),
            ("completeordermodel", "terminal_id", "INTEGER"),
            
            # liveaccount table
            ("liveaccount", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("liveaccount", "profit_share", "FLOAT DEFAULT 80.0"),
            ("liveaccount", "status", "VARCHAR DEFAULT 'active'"),
            ("liveaccount", "session_id", "VARCHAR"),
            ("liveaccount", "terminal_id", "INTEGER"),
            
            # stage2account table
            ("stage2account", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("stage2account", "profit_target", "FLOAT"),
            ("stage2account", "status", "VARCHAR DEFAULT 'active'"),
            ("stage2account", "session_id", "VARCHAR"),
            ("stage2account", "terminal_id", "INTEGER"),
            
            # ordermodel table
            ("ordermodel", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # orderimage table
            ("orderimage", "created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # passorder table
            ("passorder", "pass_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("passorder", "profit_amount", "FLOAT"),
            ("passorder", "notes", "VARCHAR"),
            
            # ordertimeline table
            ("ordertimeline", "event_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("ordertimeline", "notes", "VARCHAR"),
            
            # rejectorder table
            ("rejectorder", "rejected_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # failorder table
            ("failorder", "failed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            
            # certificate table
            ("certificate", "issue_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("certificate", "profit_target", "FLOAT"),
        ]
        
        for table_name, column_name, column_definition in order_migrations:
            try:
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND column_name = %s;
                """, (table_name, column_name))
                
                if cursor.fetchone() is None:
                    sql = f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition};'
                    cursor.execute(sql)
                    print(f"✅ Added {column_name} column to {table_name}")
                else:
                    print(f"ℹ️  {column_name} column already exists in {table_name}")
                    
            except Exception as e:
                print(f"⚠️  Error adding {column_name} to {table_name}: {str(e)}")
        
        print("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ User table now has referral columns:")
        print("   - referral_code (VARCHAR, UNIQUE)")
        print("   - referred_by (VARCHAR)")
        print("   - total_points (FLOAT, DEFAULT 0)")
        print("\n✅ All order tables now have required columns:")
        print("   - completeordermodel: completed_at, profit_target, session_id, terminal_id")
        print("   - liveaccount: created_at, profit_share, status, session_id, terminal_id")
        print("   - stage2account: created_at, profit_target, status, session_id, terminal_id")
        print("   - ordermodel: created_at")
        print("   - orderimage: created_at")
        print("   - passorder: pass_date, profit_amount, notes")
        print("   - ordertimeline: event_date, notes")
        print("   - rejectorder: rejected_at")
        print("   - failorder: failed_at")
        print("   - certificate: issue_date, profit_target")
        print("\n🚀 Your application should now work correctly on Heroku!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {str(e)}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    success = run_complete_migration()
    if not success:
        exit(1) 