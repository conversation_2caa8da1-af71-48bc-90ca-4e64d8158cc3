"use client"
import { motion } from "framer-motion"

export default function EmpowermentSection() {
  return (
    <section className="py-16 relative bg-gradient-to-b from-[#001a2c] to-[#00253c] overflow-hidden">
      {/* Subtle underwater background effects */}
      <div className="absolute inset-0 bg-gradient-radial from-sky-500/5 via-transparent to-transparent" />

      {/* Floating bubbles */}
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-sky-400/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100],
              opacity: [0, 0.6, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 8,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Larger floating elements */}
      <div className="absolute inset-0">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`large-${i}`}
            className="absolute w-4 h-4 bg-blue-400/8 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-30, -120],
              x: [0, Math.random() * 40 - 20],
              opacity: [0, 0.4, 0],
              scale: [0.3, 1.2, 0.3],
            }}
            transition={{
              duration: 12 + Math.random() * 6,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 12,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Subtle light rays */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-sky-400/20 via-sky-400/5 to-transparent"
          animate={{
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 6,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-blue-400/15 via-blue-400/3 to-transparent"
          animate={{
            opacity: [0.1, 0.4, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            delay: 2,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Gentle ripple effects */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/2 w-32 h-32 border border-sky-400/10 rounded-full"
          style={{ transform: "translate(-50%, -50%)" }}
          animate={{
            scale: [1, 2, 1],
            opacity: [0.3, 0, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-24 h-24 border border-blue-400/8 rounded-full"
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.2, 0, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            delay: 3,
            ease: "easeOut",
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold">
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Empowering traders to go from minnows to whales
            </span>
          </h2>
        </motion.div>
      </div>
    </section>
  )
}
