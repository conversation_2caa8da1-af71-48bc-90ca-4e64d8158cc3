import os
from dotenv import load_dotenv
import psycopg2
from datetime import datetime

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")

if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

try:
    # Connect to the database
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor
    cur = conn.cursor()
    
    # Test inserting a record with NULL values for session_id and terminal_id
    print("Inserting test record...")
    
    # Generate a unique order_id for testing
    import random
    test_order_id = random.randint(10000000, 99999999)
    
    cur.execute("""
        INSERT INTO completeordermodel 
        (order_id, server, platform_login, platform_password, session_id, terminal_id, profit_target, completed_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
    """, (
        test_order_id,
        "Test Server",
        "test_login",
        "test_password",
        None,  # session_id is NULL
        None,  # terminal_id is NULL
        None,  # profit_target is NULL
        datetime.utcnow()
    ))
    
    # Get the ID of the inserted record
    inserted_id = cur.fetchone()[0]
    
    # Commit the transaction
    conn.commit()
    
    print(f"Test record inserted successfully with ID: {inserted_id} and order_id: {test_order_id}")
    
    # Close the connection
    cur.close()
    conn.close()
    
except Exception as e:
    print(f"Error: {str(e)}")
    # Rollback the transaction if an error occurred
    if conn:
        conn.rollback()
