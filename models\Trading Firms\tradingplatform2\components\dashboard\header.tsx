"use client"

import { useState } from "react"
import { usePathname } from "next/navigation"
import { Bell, ChevronDown, HelpCircle, Search, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface HeaderProps {
  sidebarWidth: number
}

export default function Header({ sidebarWidth }: HeaderProps) {
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = useState("")

  // Get page title from pathname
  const getPageTitle = () => {
    const path = pathname.split("/").pop()
    if (!path || path === "dashboard") return "Dashboard"
    return path.charAt(0).toUpperCase() + path.slice(1)
  }

  return (
    <header
      className="sticky top-0 z-30 flex h-16 items-center bg-[#001a2c] border-b border-[#003a4c] px-4"
      style={{ marginLeft: `${sidebarWidth}px` }}
    >
      <div className="flex flex-1 items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-white">{getPageTitle()}</h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pl-10 bg-[#002a3c] border-[#003a4c] text-white focus:border-teal-500"
            />
          </div>

          {/* Help */}
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
            <HelpCircle className="h-5 w-5" />
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative text-gray-400 hover:text-white">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-teal-500 text-white">
                  3
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 bg-[#002a3c] border-[#003a4c] text-white">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              {[
                {
                  title: "Account Verification",
                  description: "Your KYC verification has been approved.",
                  time: "2 hours ago",
                },
                {
                  title: "Withdrawal Processed",
                  description: "Your withdrawal of $1,250 has been processed.",
                  time: "Yesterday",
                },
                {
                  title: "New Challenge Available",
                  description: "Check out our new HFT challenge with improved conditions.",
                  time: "3 days ago",
                },
              ].map((notification, index) => (
                <DropdownMenuItem
                  key={index}
                  className="flex flex-col items-start p-3 cursor-pointer hover:bg-[#003a4c]"
                >
                  <div className="font-medium">{notification.title}</div>
                  <div className="text-sm text-gray-400">{notification.description}</div>
                  <div className="text-xs text-gray-500 mt-1">{notification.time}</div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="justify-center text-teal-400 hover:bg-[#003a4c] hover:text-teal-300">
                View all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-8 flex items-center space-x-2 text-gray-400 hover:text-white"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User" />
                  <AvatarFallback className="bg-[#003a4c] text-white">JD</AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-white">John Doe</div>
                  <div className="text-xs text-gray-500">Premium Account</div>
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-[#002a3c] border-[#003a4c] text-white">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <Bell className="mr-2 h-4 w-4" />
                <span>Notifications</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <HelpCircle className="mr-2 h-4 w-4" />
                <span>Support</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
