import os
import sys
from dotenv import load_dotenv
from sqlmodel import SQLModel, create_engine, Session
import psycopg2

# Add the current directory to the Python path
sys.path.append('.')

# Load environment variables
load_dotenv()

# Get database URL
database_url = os.getenv("DATABASE_URL")
if not database_url:
    print("DATABASE_URL environment variable is not set")
    exit(1)

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Import the models
try:
    from models.order import LiveAccount, Stage2Account
    print("Successfully imported models")
except ImportError as e:
    print(f"Error importing models: {str(e)}")
    exit(1)

try:
    # Connect to the database using psycopg2
    print("Connecting to database...")
    conn = psycopg2.connect(database_url)
    print("Connected successfully")
    
    # Create a cursor and set autocommit to True
    conn.autocommit = True
    cur = conn.cursor()
    
    # Drop and recreate the tables
    print("Dropping and recreating tables...")
    
    # Drop tables if they exist
    cur.execute("DROP TABLE IF EXISTS liveaccount CASCADE")
    print("Dropped liveaccount table")
    
    cur.execute("DROP TABLE IF EXISTS stage2account CASCADE")
    print("Dropped stage2account table")
    
    # Close the psycopg2 connection
    cur.close()
    conn.close()
    
    # Create the SQLAlchemy engine
    engine = create_engine(database_url, echo=True)
    
    # Create the tables using SQLModel
    print("Creating tables with SQLModel...")
    SQLModel.metadata.create_all(engine)
    print("Tables created successfully")
    
    # Verify the changes
    print("\nVerifying changes...")
    conn = psycopg2.connect(database_url)
    cur = conn.cursor()
    
    tables_columns = [
        ("liveaccount", ["session_id", "terminal_id"]),
        ("stage2account", ["session_id", "terminal_id"])
    ]
    
    for table, columns in tables_columns:
        for column in columns:
            cur.execute(f"""
                SELECT column_name, is_nullable
                FROM information_schema.columns 
                WHERE table_name='{table}' AND column_name='{column}'
            """)
            
            result = cur.fetchone()
            if result:
                print(f"Column {result[0]} in {table} is now nullable: {result[1]}")
            else:
                print(f"Column {column} in {table} not found")
    
    # Close the connection
    cur.close()
    conn.close()
    print("\nDatabase schema update completed successfully!")
    
except Exception as e:
    print(f"Error: {str(e)}")
    if 'conn' in locals() and conn:
        conn.close()
