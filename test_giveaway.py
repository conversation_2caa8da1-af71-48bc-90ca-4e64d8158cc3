#!/usr/bin/env python3
"""
Test script to verify giveaway logic
"""

def convert_account_size_to_float(account_size: str) -> float:
    """
    Convert account size string to float value.
    Handles formats like: "50K", "50000", "100k", "100000", etc.
    """
    account_size = account_size.strip().upper()
    
    if account_size.endswith('K'):
        # Remove 'K' and multiply by 1000
        return float(account_size[:-1]) * 1000
    elif account_size.endswith('M'):
        # Remove 'M' and multiply by 1000000
        return float(account_size[:-1]) * 1000000
    else:
        # Assume it's already a number
        return float(account_size)

# Test cases
GIVEAWAY_THRESHOLD = 50000

test_cases = [
    "50K",      # Should be 50000 - QUALIFIES
    "50000",    # Should be 50000 - QUALIFIES  
    "100K",     # Should be 100000 - QUALIFIES
    "25K",      # Should be 25000 - DOES NOT QUALIFY
    "25000",    # Should be 25000 - DOES NOT QUALIFY
    "49K",      # Should be 49000 - DOES NOT QUALIFY
    "49000",    # Should be 49000 - DOES NOT QUALIFY
    "51K",      # Should be 51000 - QUALIFIES
    "51000",    # Should be 51000 - QUALIFIES
    "1M",       # Should be 1000000 - QUALIFIES
    "1000000",  # Should be 1000000 - QUALIFIES
]

print("Testing Giveaway Logic")
print("=" * 50)
print(f"Giveaway Threshold: {GIVEAWAY_THRESHOLD}")
print()

for test_case in test_cases:
    converted_value = convert_account_size_to_float(test_case)
    qualifies = converted_value >= GIVEAWAY_THRESHOLD
    status = "✅ QUALIFIES" if qualifies else "❌ DOES NOT QUALIFY"
    
    print(f"Account Size: {test_case:>8} -> {converted_value:>10} -> {status}")

print()
print("Summary:")
print("- Orders with account size >= 50000 will be added to giveaway")
print("- Orders with account size < 50000 will NOT be added to giveaway")
print("- The system handles formats: '50K', '50000', '100K', '1M', etc.") 