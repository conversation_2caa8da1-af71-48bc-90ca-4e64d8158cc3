import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

def verify_table_schema(table_name, expected_columns):
    """
    Verify that a table has the expected columns
    """
    print(f"\nChecking {table_name} table schema...")
    
    # Connect to the database
    conn = psycopg2.connect(database_url)
    cursor = conn.cursor()
    
    try:
        # Get all columns for the table
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name=%s
            ORDER BY ordinal_position;
        """, (table_name,))
        
        columns = cursor.fetchall()
        existing_columns = [col[0] for col in columns]
        
        print(f"Table {table_name} has {len(columns)} columns:")
        for column in columns:
            column_name, data_type, is_nullable, column_default = column
            default_str = str(column_default) if column_default else "None"
            if len(default_str) > 15:
                default_str = default_str[:12] + "..."
            print(f"  {column_name:<20} {data_type:<15} nullable:{is_nullable:<5} default:{default_str}")
        
        # Check for expected columns
        print(f"\nChecking for expected columns in {table_name}:")
        for col in expected_columns:
            if col in existing_columns:
                print(f"  ✓ {col}")
            else:
                print(f"  ✗ {col} - MISSING")
        
        return existing_columns
        
    except Exception as e:
        print(f"Error checking {table_name} schema: {str(e)}")
        return []
    finally:
        cursor.close()
        conn.close()

def main():
    """
    Verify schemas for key tables
    """
    print("Verifying database schemas...")
    
    # Check user table
    user_expected = ['id', 'username', 'email', 'hashed_password', 'name', 'phone_no', 'country', 'address', 'created_at', 'referral_code', 'referred_by', 'total_points']
    verify_table_schema('user', user_expected)
    
    # Check completeordermodel table
    complete_order_expected = ['id', 'order_id', 'server', 'platform_login', 'platform_password', 'session_id', 'terminal_id', 'profit_target', 'completed_at']
    verify_table_schema('completeordermodel', complete_order_expected)
    
    # Check stage2account table
    stage2_expected = ['id', 'order_id', 'server', 'platform_login', 'platform_password', 'session_id', 'terminal_id', 'created_at']
    verify_table_schema('stage2account', stage2_expected)
    
    # Check liveaccount table
    live_expected = ['id', 'order_id', 'server', 'platform_login', 'platform_password', 'session_id', 'terminal_id', 'created_at']
    verify_table_schema('liveaccount', live_expected)
    
    print("\nSchema verification completed!")

if __name__ == "__main__":
    main()
