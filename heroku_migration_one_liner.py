#!/usr/bin/env python3
"""
One-liner migration for Hero<PERSON>
Run this directly on Heroku to fix the missing referral columns.
"""

import os, psycopg2, uuid
from dotenv import load_dotenv

load_dotenv()

def generate_referral_code():
    return str(uuid.uuid4())[:8].upper()

# Get DATABASE_URL and fix format
database_url = os.getenv("DATABASE_URL")
if database_url.startswith("postgres://"):
    database_url = database_url.replace("postgres://", "postgresql://", 1)

print("Connecting to Heroku database...")
conn = psycopg2.connect(database_url)
conn.autocommit = True
cursor = conn.cursor()

try:
    # Add referral_code column if it doesn't exist
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='user' AND column_name='referral_code';")
    if cursor.fetchone() is None:
        cursor.execute('ALTER TABLE "user" ADD COLUMN referral_code VARCHAR;')
        cursor.execute('ALTER TABLE "user" ADD CONSTRAINT user_referral_code_unique UNIQUE (referral_code);')
        print("✅ Added referral_code column")
    else:
        print("ℹ️  referral_code column already exists")
    
    # Add referred_by column if it doesn't exist
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='user' AND column_name='referred_by';")
    if cursor.fetchone() is None:
        cursor.execute('ALTER TABLE "user" ADD COLUMN referred_by VARCHAR;')
        print("✅ Added referred_by column")
    else:
        print("ℹ️  referred_by column already exists")
    
    # Add total_points column if it doesn't exist
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='user' AND column_name='total_points';")
    if cursor.fetchone() is None:
        cursor.execute('ALTER TABLE "user" ADD COLUMN total_points FLOAT DEFAULT 0;')
        print("✅ Added total_points column")
    else:
        print("ℹ️  total_points column already exists")
    
    # Update existing users with referral codes
    cursor.execute('SELECT id FROM "user" WHERE referral_code IS NULL;')
    users_without_codes = cursor.fetchall()
    if users_without_codes:
        print(f"Updating {len(users_without_codes)} users with referral codes...")
        for user_row in users_without_codes:
            user_id = user_row[0]
            new_code = generate_referral_code()
            while True:
                cursor.execute('SELECT id FROM "user" WHERE referral_code = %s;', (new_code,))
                if cursor.fetchone() is None:
                    break
                new_code = generate_referral_code()
            cursor.execute('UPDATE "user" SET referral_code = %s WHERE id = %s;', (new_code, user_id))
        print(f"✅ Updated {len(users_without_codes)} users")
    else:
        print("ℹ️  All users already have referral codes")
    
    print("\n🎉 Migration completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {str(e)}")
finally:
    cursor.close()
    conn.close() 