"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, Lock, Mail, User, Shield, ArrowRight, Github, Google, CheckCircle } from "lucide-react"
import Link from "next/link"

export default function SignUpPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    accountType: "",
    agreeToTerms: false
  })

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // Add your signup logic here
    setTimeout(() => setIsLoading(false), 2000)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  }

  const accountTypes = [
    { value: "individual", label: "Individual Trader" },
    { value: "professional", label: "Professional Trader" },
    { value: "institutional", label: "Institutional" }
  ]

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-[#001a2c] to-[#000c14]">
      <motion.div
        className="w-full max-w-md"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl overflow-hidden">
          <CardHeader className="space-y-4 pb-6">
            <motion.div variants={itemVariants}>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center mb-4 mx-auto">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold text-center text-white">Create Account</CardTitle>
              <CardDescription className="text-center text-gray-400">
                Join our trading platform in a few steps
              </CardDescription>
            </motion.div>

            <div className="flex justify-center space-x-2 pt-4">
              {[1, 2].map((i) => (
                <motion.div
                  key={i}
                  className={`h-2 rounded-full ${
                    i === step ? "w-8 bg-sky-500" : "w-2 bg-[#004c66]"
                  }`}
                  animate={{ backgroundColor: i === step ? "#0ea5e9" : "#004c66" }}
                  transition={{ duration: 0.3 }}
                />
              ))}
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <motion.div
              key={step}
              custom={step}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
            >
              {step === 1 ? (
                <div className="space-y-4">
                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label htmlFor="fullName" className="text-white">Full Name</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <Input
                        id="fullName"
                        value={formData.fullName}
                        onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                        placeholder="Enter your full name"
                        className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      />
                    </div>
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label htmlFor="email" className="text-white">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        placeholder="Enter your email"
                        className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      />
                    </div>
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label htmlFor="password" className="text-white">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                        placeholder="Create a strong password"
                        className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </Button>
                    </div>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <Button
                      onClick={() => setStep(2)}
                      className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                    >
                      Continue
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </motion.div>
                </div>
              ) : (
                <div className="space-y-4">
                  <motion.div variants={itemVariants} className="space-y-2">
                    <Label className="text-white">Account Type</Label>
                    <Select
                      value={formData.accountType}
                      onValueChange={(value) => setFormData({ ...formData, accountType: value })}
                    >
                      <SelectTrigger className="w-full bg-[#001a2c] border-[#004c66] text-white h-12">
                        <SelectValue placeholder="Select account type" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#001a2c] border-[#004c66]">
                        {accountTypes.map((type) => (
                          <SelectItem
                            key={type.value}
                            value={type.value}
                            className="text-white hover:bg-[#002a3c]"
                          >
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="terms"
                        checked={formData.agreeToTerms}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, agreeToTerms: checked as boolean })
                        }
                        className="border-[#004c66] data-[state=checked]:bg-sky-500"
                      />
                      <label
                        htmlFor="terms"
                        className="text-sm text-gray-400"
                      >
                        I agree to the{" "}
                        <Link href="/terms" className="text-sky-400 hover:text-sky-300">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="text-sky-400 hover:text-sky-300">
                          Privacy Policy
                        </Link>
                      </label>
                    </div>
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <Button
                      onClick={handleSignUp}
                      disabled={!formData.agreeToTerms || isLoading}
                      className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                    >
                      {isLoading ? (
                        <motion.div
                          className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                      ) : (
                        <>
                          Create Account
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => setStep(1)}
                      className="w-full text-gray-400 hover:text-white"
                    >
                      Back to previous step
                    </Button>
                  </motion.div>
                </div>
              )}
            </motion.div>

            <motion.div variants={itemVariants}>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-[#004c66]"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 text-gray-400 bg-[#001a2c]">Or sign up with</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-6">
                <Button 
                  variant="outline" 
                  className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                >
                  <Google className="h-4 w-4" />
                  <span>Google</span>
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full border-[#004c66] text-white hover:bg-[#003a4c] space-x-2"
                >
                  <Github className="h-4 w-4" />
                  <span>GitHub</span>
                </Button>
              </div>
            </motion.div>
          </CardContent>

          <CardFooter>
            <motion.p variants={itemVariants} className="text-center text-gray-400 text-sm w-full">
              Already have an account?{" "}
              <Link href="/auth/login" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                Sign In
              </Link>
            </motion.p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
} 