"use client"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, Share2, Trophy, Award, CheckCircle, Clock } from "lucide-react"

export default function CertificatesPage() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const certificates = [
    {
      id: "cert1",
      title: "Standard $100K Challenge",
      type: "Challenge Completion",
      date: "May 15, 2025",
      status: "issued",
      description: "Successfully completed the Standard $100K Challenge with a profit of 9.2%",
    },
    {
      id: "cert2",
      title: "HFT Neo $50K Challenge",
      type: "Challenge Completion",
      date: "April 10, 2025",
      status: "issued",
      description: "Successfully completed the HFT Neo $50K Challenge with a profit of 7.5%",
    },
    {
      id: "cert3",
      title: "Advanced Risk Management",
      type: "Educational Achievement",
      date: "March 5, 2025",
      status: "issued",
      description: "Completed the Advanced Risk Management course with distinction",
    },
    {
      id: "cert4",
      title: "Standard $200K Challenge",
      type: "Challenge Completion",
      date: "Pending",
      status: "pending",
      description: "Certificate will be issued upon successful completion of the challenge",
    },
  ]

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Certificates & Achievements</h2>
        <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
          <Trophy className="mr-2 h-4 w-4" />
          View All Achievements
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {certificates
          .filter((cert) => cert.status === "issued")
          .map((certificate, index) => (
            <motion.div
              key={certificate.id}
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: index * 0.1 }}
            >
              <Card className="bg-[#002a3c] border-[#003a4c] h-full">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-white">{certificate.title}</CardTitle>
                      <CardDescription className="text-gray-400">{certificate.type}</CardDescription>
                    </div>
                    <Badge className="bg-teal-500/20 text-teal-400">Issued</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center mb-4">
                    <Award className="h-10 w-10 text-yellow-500 mr-3" />
                    <div>
                      <p className="text-white">{certificate.description}</p>
                      <p className="text-sm text-gray-400">Issued on: {certificate.date}</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between border-t border-[#003a4c] pt-4">
                  <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                    <Share2 className="mr-2 h-4 w-4" />
                    Share
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Pending Certificates</CardTitle>
            <CardDescription className="text-gray-400">
              Certificates that will be issued upon completion of requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {certificates
                .filter((cert) => cert.status === "pending")
                .map((certificate, index) => (
                  <div
                    key={certificate.id}
                    className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]"
                  >
                    <div className="flex items-center">
                      <Clock className="h-8 w-8 text-gray-500 mr-4" />
                      <div>
                        <p className="text-white font-medium">{certificate.title}</p>
                        <p className="text-sm text-gray-400">{certificate.description}</p>
                      </div>
                    </div>
                    <Badge className="bg-yellow-500/20 text-yellow-400">Pending</Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Available Achievements</CardTitle>
            <CardDescription className="text-gray-400">
              Complete these milestones to earn additional certificates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  title: "Consistent Trader",
                  description: "Maintain a positive balance for 30 consecutive trading days",
                  progress: 65,
                  icon: <CheckCircle className="h-10 w-10 text-sky-500" />,
                },
                {
                  title: "Risk Master",
                  description: "Complete 50 trades with a risk-to-reward ratio of at least 1:2",
                  progress: 40,
                  icon: <Trophy className="h-10 w-10 text-sky-500" />,
                },
                {
                  title: "Profit Champion",
                  description: "Achieve a 20% profit in a single month on any account",
                  progress: 10,
                  icon: <Award className="h-10 w-10 text-sky-500" />,
                },
              ].map((achievement, index) => (
                <div key={index} className="flex flex-col p-6 bg-[#001a2c] rounded-lg border border-[#003a4c] h-full">
                  <div className="flex items-center mb-4">
                    {achievement.icon}
                    <h3 className="text-white font-medium ml-3">{achievement.title}</h3>
                  </div>
                  <p className="text-gray-400 text-sm mb-4">{achievement.description}</p>
                  <div className="mt-auto">
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-gray-400">Progress</span>
                      <span className="text-sm font-medium text-white">{achievement.progress}%</span>
                    </div>
                    <div className="h-2 bg-[#003a4c] rounded-full overflow-hidden">
                      <div className="h-full bg-teal-500" style={{ width: `${achievement.progress}%` }}></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.5 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Certificate Verification</CardTitle>
            <CardDescription className="text-gray-400">
              All certificates issued by Funded Whales can be verified for authenticity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row items-center justify-between p-6 bg-[#001a2c] rounded-lg border border-[#003a4c]">
              <div className="flex items-center mb-4 md:mb-0">
                <FileText className="h-10 w-10 text-teal-500 mr-4" />
                <div>
                  <h3 className="text-white font-medium">Verify Certificate Authenticity</h3>
                  <p className="text-gray-400">
                    Each certificate has a unique verification code that can be used to confirm its authenticity.
                  </p>
                </div>
              </div>
              <Button className="bg-teal-500 hover:bg-teal-600 text-white">Verify Certificate</Button>
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              For any questions about certificates or achievements, please contact our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}
