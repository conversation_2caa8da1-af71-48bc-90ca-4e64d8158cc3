import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API base URL from environment variables or use default
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

def test_assign_account_with_profit_target():
    """
    Test assigning an account to an order with a profit target
    
    This test:
    1. Creates a new account credential
    2. Creates a new order
    3. Assigns the account to the order with a profit target
    4. Verifies the profit target is saved in the complete order record
    """
    print("Starting test: Assign account with profit target")
    
    # Step 1: Create a new account credential
    print("\nStep 1: Creating a new account credential...")
    credentials_data = {
        "credentials": [
            {
                "platform": "MT4",
                "server": "Test Server",
                "platform_login": f"test_login_{os.urandom(4).hex()}",
                "platform_password": "test_password",
                "account_size": "10K",
                "account_type": "phase1"
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/account/credentials",
            json=credentials_data
        )
        response.raise_for_status()
        credential = response.json()[0]
        credential_id = credential["id"]
        print(f"  Created credential with ID: {credential_id}")
    except Exception as e:
        print(f"  Error creating credential: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"  Response: {e.response.text}")
        return
    
    # Step 2: Create a new order
    print("\nStep 2: Creating a new order...")
    order_data = {
        "username": "test_user",
        "email": "<EMAIL>",
        "challenge_type": "Test Challenge",
        "account_size": "10K",
        "platform": "MT4",
        "payment_method": "Test Payment",
        "txid": f"test_txid_{os.urandom(4).hex()}"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/order/create_order",
            data=order_data
        )
        response.raise_for_status()
        order = response.json()
        order_id = order["id"]
        print(f"  Created order with ID: {order_id}")
    except Exception as e:
        print(f"  Error creating order: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"  Response: {e.response.text}")
        return
    
    # Step 3: Assign the account to the order with a profit target
    print("\nStep 3: Assigning account to order with profit target...")
    profit_target = 1000.0
    
    try:
        response = requests.put(
            f"{API_BASE_URL}/account/credentials/{credential_id}/assign/{order_id}?profit_target={profit_target}"
        )
        response.raise_for_status()
        result = response.json()
        print(f"  Account assigned successfully: {result}")
    except Exception as e:
        print(f"  Error assigning account: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"  Response: {e.response.text}")
        return
    
    # Step 4: Verify the profit target is saved in the complete order record
    print("\nStep 4: Verifying profit target in complete order record...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/order/complete_orders"
        )
        response.raise_for_status()
        complete_orders = response.json()
        
        # Find the complete order for our order_id
        found = False
        for complete_order in complete_orders:
            if complete_order["order_id"] == order_id:
                found = True
                saved_profit_target = complete_order.get("profit_target")
                if saved_profit_target == profit_target:
                    print(f"  Success! Profit target {profit_target} was correctly saved in the complete order record.")
                else:
                    print(f"  Error: Profit target mismatch. Expected {profit_target}, got {saved_profit_target}")
                break
        
        if not found:
            print(f"  Error: Complete order record for order ID {order_id} not found")
    except Exception as e:
        print(f"  Error verifying profit target: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"  Response: {e.response.text}")
        return
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_assign_account_with_profit_target()
